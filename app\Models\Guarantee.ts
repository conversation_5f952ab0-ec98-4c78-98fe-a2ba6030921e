import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, HasMany, hasMany } from '@ioc:Adonis/Lucid/Orm'
import GuaranteeType from './GuaranteeType'
import InsuranceCompany from './InsuranceCompany'
import PackageGuarantee from './PackageGuarantee'

export default class Guarantee extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number | null

  @column({ columnName: 'guarantee_type_id' })
  public guaranteeTypeId: number

  @column()
  public name: string

  @column()
  public code: string | null

  @column()
  public description: string | null

  @column({ columnName: 'is_active' })
  public isActive: boolean = true

  @column({ columnName: 'metadata' })
  public metadata: {
    requires_prescription?: boolean,
    age_min?: number,
    age_max?: number,
    allowed_providers?: string[]
  } | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column.dateTime()
  public deletedAt: DateTime | null

  @belongsTo(() => GuaranteeType, {
    foreignKey: 'guaranteeTypeId',
    localKey: 'id',
  })
  public guarantee_type: BelongsTo<typeof GuaranteeType>

  @belongsTo(() => InsuranceCompany, {
    foreignKey: 'insuranceCompanyId',
    localKey: 'id',
  })
  public insurance_company: BelongsTo<typeof InsuranceCompany>

  @hasMany(() => PackageGuarantee, {
    foreignKey: 'guaranteeId',
    localKey: 'id',
  })
  public package_guarantees: HasMany<typeof PackageGuarantee>
}
