import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, HasOne, hasOne, afterFetch, beforeSave } from '@ioc:Adonis/Lucid/Orm'
import PaymentType from './PaymentType'
import User from './User'
import Patient from './Patient'
import { Payment } from './Payment'

export default class PaymentIntent extends BaseModel {
  @column({ isPrimary: true, columnName: 'id' })
  public id: number

  @column({ columnName: 'reference' })
  public reference: string

  @column({ columnName: 'payment_type_id' })
  public paymentTypeId: number

  @column({ columnName: 'user_id' })
  public userId: number

  @column({ columnName: 'beneficiary_id' })
  public beneficiaryId?: number

  @column({ columnName: 'currency' })
  public currency: string

  @column({ columnName: 'client' })
  public client?: any

  @column({ columnName: 'amount_paid' })
  public amountPaid: number

  @column({ columnName: 'fees' })
  public fees?: number

  @column({ columnName: 'payment_method' })
  public paymentMethod?: string

  @column({ columnName: 'phone' })
  public phone?: string

  @column({ columnName: 'network' })
  public network?: string

  @column({ columnName: 'metadata' })
  public metadata?: any

  @column({ columnName: 'payment_link' })
  public paymentLink?: string

  @column({ columnName: 'callback_url' })
  public callbackUrl?: string

  @column({ columnName: 'redirect_url' })
  public redirectUrl?: string

  @column({ columnName: 'qrcode_url' })
  public qrcodeUrl?: string

  @column({ columnName: 'status' })
  public status: 'pending' | 'paid' | 'cancelled' | 'failed'

  @column({ columnName: 'description' })
  public description?: string

  @column.dateTime({ columnName: 'request_at' })
  public requestAt?: DateTime

  @column.dateTime({ columnName: 'paid_at' })
  public paidAt?: DateTime

  @column.dateTime({ columnName: 'canceled_at' })
  public canceledAt?: DateTime

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  @belongsTo(() =>PaymentType,{
    foreignKey: 'paymentTypeId',
    localKey: 'id'
  })
  public paymentType: BelongsTo<typeof PaymentType>

  @belongsTo(() => User, {
    foreignKey: 'userId',
    localKey: 'id'
  })
  public user: BelongsTo<typeof User>

  @belongsTo(() => Patient, {
    foreignKey: 'beneficiaryId',
    localKey: 'id'
  })
  public beneficiary: BelongsTo<typeof Patient>

  @hasOne(() => Payment,{
    foreignKey: 'paymentIntentId',
    localKey: 'id'
  })
  public payment: HasOne<typeof Payment>

  @afterFetch()
  public static parseMetadata(intent: PaymentIntent) {
    if (intent.metadata && typeof intent.metadata === 'string') {
      try {
        intent.metadata = JSON.parse(intent.metadata);
      } catch (error) {
        console.log("Error parsing PaymentIntent metadata:", error);
        intent.metadata = {};
      }
    }
  }

  @beforeSave()
  public static stringifyMetadata(intent: PaymentIntent) {
    if (intent.metadata && typeof intent.metadata === 'object') {
      try {
        intent.metadata = JSON.stringify(intent.metadata);
      } catch (error) {
        console.log("Error stringifying PaymentIntent metadata:", error);
        intent.metadata = '{}';
      }
    }
  }
}
