
import { schema } from '@ioc:Adonis/Core/Validator';
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { ApiResponse } from 'App/Controllers/interfaces'
import Patient from 'App/Models/Patient';
import User from 'App/Models/User';
import Database from '@ioc:Adonis/Lucid/Database';
import { Payment } from 'App/Models/Payment';
import Transaction from 'App/Models/Transaction';
import Order from 'App/Models/Order';
import Diagnostic from 'App/Models/Diagnostic';
import Delivery from 'App/Models/Delivery';
import Appointment from 'App/Models/Appointment';
import VitalParameter from 'App/Models/VitalParameter';

export default class PatientController {

  public async getPatients({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {} as ApiResponse;
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;
      const sort = request.input('sort') || 'created_at';
      const order = request.input('order') || 'desc';
      const countryId = request.input('country_id', null);
      const cityId = request.input('city_id', null);
      const districtId = request.input('district_id', null);
      const hasInsurance = request.input('has_insurance', null);
      const carnetIsActive = request.input('carnet_is_active', null);
      const status = request.input('status', null) as 'activated' | 'archived' | 'all' | 'validated' | 'rejected' | 'blocked' | 'pending' | 'kyc';
      const search = request.input('search', null);

      const filters = {
        country_id: countryId,
        city_id: cityId,
        quarter_id: districtId,
        has_insurance: hasInsurance,
        carnet_is_active: carnetIsActive,
        status: status,
      }

      const query = Patient.query().orderBy(sort, order).preload('country').preload('city').preload('quarter').preload('bloodGroup');

      if (Object.keys(filters).length > 0) {
        for (const [key, value] of Object.entries(filters)) {
          if (value) {
            query.where(key, value);
          }

        }
      }
      if (search) {
        query.where((query) => {
          query.where('first_name', 'like', `%${search}%`);
          query.orWhere('last_name', 'like', `%${search}%`);
          query.orWhere('phone', 'like', `%${search}%`);
          query.orWhere('email', 'like', `%${search}%`);
          query.orWhere('code', 'like', `%${search}%`);
        })
      }
      const patients = await query.paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Liste des patients trouvés",
        result: patients
      }
    } catch (error) {
      status = 500;
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: "Echec de récupération des données",
        result: null,
        except: error.message
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async getPatientDetails({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {} as ApiResponse;
    let status = 200;
    try {
      const patientId = request.input('patient_id');
      if (!patientId) {
        status = 422;
        apiResponse = {
          success: false,
          message: "Veuillez fournir un identifiant de patient",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      const patient = await Patient.query().where('id', patientId)
        .preload('country')
        .preload('city')
        .preload('quarter')
        .preload('bloodGroup')
        .preload('user')
        .preload('carnet')
        .preload('wallet')
        .preload('healthInstitute')
        .limit(1)
      .first();

      if (!patient) {
        status = 404;
        apiResponse = {
          success: false,
          message: "Aucun patient trouvé",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      apiResponse = {
        success: true,
        message: "Patient trouvé",
        result: patient
      }

    } catch (error) {
      status = 500;
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: "Echec de récupération des données",
        result: null,
        except: error.message
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async getPatientVitalParams({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
      except: null,
    };
    let status = 200;
    try {
      let patientId = request.input('patient_id');
      let page = request.input('page', 1);
      let limit = request.input('limit', 50);

      const patient = await Patient.query().where('id', patientId).first();
      if (!patient) {
        apiResponse = {
          success: false,
          message: 'Patient not found',
          result: null,
        };
        return response.status(404).json(apiResponse);
      }
      const vitalParams = await VitalParameter.query().where('patient_id', patient.id).orderBy('created_at', 'desc')
        .preload('diagnostic').preload('creator').preload('vitalExam')
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: 'Vital Params retrieved successfully',
        result: vitalParams,
        except: null,
      };
    } catch (error) {
      apiResponse = {
        success: false,
        message: "Echec de récupération des données du patient",
        result: null,
        except: error.message,
      };
    }
    return response.status(status).json(apiResponse);
  }

  public async updatePatient({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {} as ApiResponse;
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          patient_id: schema.number(),
          first_name: schema.string.optional(),
          last_name: schema.string.optional(),
          phone: schema.string.optional(),
          email: schema.string.optional(),
          birthday_year: schema.number.optional(),
          birthday_month: schema.number.optional(),
          birthday_day: schema.number.optional(),
          gender: schema.string.optional(),
          situation_matrimonial: schema.string.optional(),
          blood_group_id: schema.number.optional(),
        })
      });
      const {
        patient_id, first_name, last_name, phone, email, birthday_year, birthday_month, birthday_day, gender, situation_matrimonial, blood_group_id
      } = payload;

      const patient = await Patient.query().where('id', patient_id).forUpdate().first();
      if (!patient) {
        status = 404;
        apiResponse = {
          success: false,
          message: "Aucun patient trouvé",
          result: null
        }
        return response.status(status).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        if (last_name || first_name || email || phone) {
          const user = await User.query().where('id', patient.user_id).forUpdate().first();
          if (user) {
            let last = last_name ? last_name.toUpperCase() : null;
            let first = first_name ? first_name.toLowerCase() : null;
            let username = last ? `${last}. ${first}` : null;
            user.merge({
              username: username !== null ? username : user.username,
              email: email || user.email,
              phone: phone || user.phone
            });
            await user.useTransaction(trx).save();
          }
        }

        await patient.merge({
          last_name: last_name !== null ? last_name : patient.last_name,
          first_name: first_name !== null ? first_name : patient.first_name,
          birthday_year: birthday_year !== null ? birthday_year : patient.birthday_year,
          birthday_month: birthday_month !== null ? birthday_month : patient.birthday_month,
          birthday_day: birthday_day !== null ? birthday_day : patient.birthday_day,
          gender: gender !== null ? gender : patient.gender,
          phone: phone !== null ? phone : patient.phone,
          email: email !== null ? email : patient.email,
          blood_group_id: blood_group_id !== null ? blood_group_id : patient.blood_group_id,
          situation_matrimoniale: situation_matrimonial !== null ? situation_matrimonial : patient.situation_matrimoniale,
        }).useTransaction(trx).save();

        await trx.commit();

        apiResponse = {
          success: true,
          message: "Patient updated",
          result: patient
        }
        return response.status(201).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        status = 500;
        console.log("error", error.message);
        apiResponse = {
          success: false,
          message: "Echec de mise à jour des données du patient",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      status = 500;
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: "Echec de mise à jour des données du patient",
        result: null,
        except: error.message,
        errors: error.messages,
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async archivedPatient({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {} as ApiResponse;
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          patient_code: schema.string(),
        })
      });
      const {
        patient_code
      } = payload;
      const patient = await Patient.query().where('id', patient_code).orWhere('code', patient_code).forUpdate().first();
      if (!patient) {
        status = 404;
        apiResponse = {
          success: false,
          message: "Aucun patient trouvé",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      await patient.merge({
        status: 'archived'
      }).save();
      apiResponse = {
        success: true,
        message: "Patient archivé avec succès",
        result: patient
      }
    } catch (error) {
      status = 500;
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: "Echec de mise à jour des données du patient",
        result: null
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async changeStatus({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          patient_id: schema.number.optional(),
          patient_code: schema.string.optional(),
          patient_status: schema.enum(['activated', 'archived', 'validated', 'rejected', 'blocked', 'pending', 'kyc']),
        })
      });

      const {
        patient_id, patient_code, patient_status
      } = payload;
      const query = Patient.query();
      if(patient_id){
        query.where('id', patient_id);
      }
      if(patient_code){
        query.orWhere('code', patient_code);
      }
      const patient = await query.forUpdate().first();
      if (!patient) {
        status = 404;
        apiResponse = {
          success: false,
          message: "Aucun patient trouvé",
          result: null
        }
        return response.status(status).json(apiResponse);
      }

      await patient.merge({
        status: patient_status
      }).save();

      apiResponse = {
        success: true,
        message: "Statut du patient mis à jour",
        result: patient
      }

    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de mise à jour du statut du patient",
        result: null
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async getPatientWalletDeposits({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 20;
      const patientId = request.input('patient_id');

      if(!patientId) {
        apiResponse = {
          success: false,
          message: "patient_id is required",
          result: null
        }
        return response.status(status).json(apiResponse);
      }

      const patient = await Patient.query().select('id').where('id', patientId).first();
      if(!patient) {
        apiResponse = {
          success: false,
          message: "Patient non trouvé",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      const deposits = await Payment.query().where('patient_id', patientId).orderBy('created_at', 'desc').paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Liste des dépôts trouvés",
        result: deposits
      }
    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de récupération des données",
        result: null
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async getPatientTransactions({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 20;
      const patient_id = request.input('patient_id', null);
      const payment_type_id = request.input('payment_type_id',null);

      const query = Transaction.query().where('patient_id', patient_id).orderBy('created_at', 'desc').preload('paymentType').preload('payer');
      if(payment_type_id){
        query.where('payment_type_id', payment_type_id);
      }
      const transactions = await query.paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Liste des transactions trouvés",
        result: transactions
      }

      apiResponse = {
        success: true,
        message: "Liste des transactions trouvés",
        result: transactions
      }
    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de récupération des données",
        result: null
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async getPatientConsultations({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit',20);
      const patient_id = request.input('patient_id');

      if(!patient_id) {
        apiResponse = {
          success: false,
          message: "patient_id is required",
          result: null
        }
        return response.status(status).json(apiResponse);
      }

      const patient = await Patient.query().select('id').where('id', patient_id).first();
      if(!patient) {
        apiResponse = {
          success: false,
          message: "Patient non trouvé",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      const consultations = await Diagnostic.query().where('patient_id', patient_id).orderBy('created_at', 'desc')
        .preload('category').preload('pathology').preload('prescriptions').preload('analyze_asks')
      .paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Liste des consultations trouvés",
        result: consultations
      }

    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de récupération des données",
        result: null
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async getPatientOrders({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit',20);
      const patient_id = request.input('patient_id');

      if(!patient_id) {
        apiResponse = {
          success: false,
          message: "patient_id is required",
          result: null
        }
        return response.status(status).json(apiResponse);
      }

      const patient = await Patient.query().select('id').where('id', patient_id).first();
      if(!patient) {
        apiResponse = {
          success: false,
          message: "Patient non trouvé",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      const orders = await Order.query().where('patient_id', patient_id).orderBy('created_at', 'desc').preload('items').paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Liste des commandes trouvés",
        result: orders
      }
    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de récupération des données",
        result: null
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async getPatientDeliveries({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit',20);
      const patient_id = request.input('patient_id');
      if(!patient_id) {
        apiResponse = {
          success: false,
          message: "patient_id is required",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      const patient = await Patient.query().select('id').where('id', patient_id).first();
      if(!patient) {
        apiResponse = {
          success: false,
          message: "Patient non trouvé",
          result: null
        }
        return response.status(status).json(apiResponse);
      }

      const deliveries = await Delivery.query().where('patient_id', patient_id).orderBy('created_at', 'desc').paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Liste des commandes trouvés",
        result: deliveries
      }

    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de récupération des données",
        result: null
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async getPatientAppointments({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit',20);
      const patient_id = request.input('patient_id');
      if(!patient_id) {
        apiResponse = {
          success: false,
          message: "patient_id is required",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      const patient = await Patient.query().select('id').where('id', patient_id).first();
      if(!patient) {
        apiResponse = {
          success: false,
          message: "Patient non trouvé",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      const appointments = await Appointment.query().where('patient_id', patient_id).orderBy('created_at', 'desc').paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Liste des commandes trouvés",
        result: appointments
      }
    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de récupération des données",
        result: null
      }
    }
  }

  // public async getPatientInsuranceCompanies({ request, response }: HttpContextContract) { }


}
