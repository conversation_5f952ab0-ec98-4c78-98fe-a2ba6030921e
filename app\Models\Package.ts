import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, column, HasMany, hasMany, ManyToMany, manyToMany } from '@ioc:Adonis/Lucid/Orm'
import Product from './Product'
import Analyze from './Analyze'
import InsuranceCompany from './InsuranceCompany'
import { belongsTo } from '@ioc:Adonis/Lucid/Orm'
import InsuranceYear from './InsuranceYear'
import PackageUpdate from './PackageUpdate'
import TeamGroup from './TeamGroup'
import Team from './Team'
import Guarantee from './Guarantee'

export default class Package extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column({columnName: 'insurance_company_id'})
  public insuranceCompanyId: number

  @column({ columnName: 'is_custom' })
  public isCustom: boolean = false

  @column({ columnName: 'team_id' })
  public teamId: number | null

  @column({ columnName: 'team_group_id' })
  public teamGroupId: number | null

  @column()
  public price: number | null

  @column()
  public plafond: number | null

  @column()
  public taux: number

  @column()
  public validity: number

  @column({ columnName: 'type' })
  public type: 'individual' | 'team'

  @column({ columnName: 'visibility' })
  public visibility: 'public' | 'private'

  @column()
  public status: string

  @column()
  public payment_type: 'annual' | 'monthly'

  @column()
  public description: string

  @column()
  public settings: any

  @column({ columnName: 'plafond_config' })
  public plafondConfig: any | null

  @column({ columnName: 'fees_config' })
  public feesConfig: any | null

  @column({ columnName: 'tranches_config' })
  public tranchesConfig: any | null

  @column({columnName: 'version'})
  public version: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @manyToMany(() => Product, {
    pivotTable: 'package_products',
    pivotColumns: ['package_id', 'product_id','quantity','public_price','is_active'],
    pivotForeignKey: 'package_id',
    pivotRelatedForeignKey: 'product_id',
    pivotTimestamps: true,
  })
  public products: ManyToMany<typeof Product>

  @manyToMany(() => Analyze, {
    pivotTable: 'package_analyzes',
    pivotColumns: ['package_id', 'analyze_id','public_price','is_active'],
    pivotForeignKey: 'package_id',
    pivotRelatedForeignKey: 'analyze_id',
    pivotTimestamps: true,
  })
  public analyzes: ManyToMany<typeof Analyze>

  @manyToMany(() => Guarantee, {
    pivotTable: 'package_guarantees',
    pivotColumns: ['package_id', 'guarantee_id', 'coverage_rate', 'coverage_limit', 'limit_type', 'limit_value', 'is_active'],
    pivotForeignKey: 'package_id',
    pivotRelatedForeignKey: 'guarantee_id',
    pivotTimestamps: true,
  })
  public guarantees: ManyToMany<typeof Guarantee>

  @belongsTo(() => InsuranceCompany, {
    foreignKey: 'insuranceCompanyId',
    localKey: 'id',
  })
  public insurance_company: BelongsTo<typeof InsuranceCompany>

  @manyToMany(() => InsuranceYear, {
    pivotTable: 'insurance_year_packages',
    pivotColumns: ['insurance_year_id', 'package_id', 'status'],
    pivotForeignKey: 'package_id',
    pivotRelatedForeignKey: 'insurance_year_id',
    pivotTimestamps: true,
  })
  public insurance_years: ManyToMany<typeof InsuranceYear>

  @hasMany(() => PackageUpdate, {
    foreignKey: 'packageId',
    localKey: 'id',
  })
  public updates: HasMany<typeof PackageUpdate>

  @belongsTo(() => Team, {
    foreignKey: 'teamId',
    localKey: 'id',
  })
  public team: BelongsTo<typeof Team>

  @belongsTo(() => TeamGroup, {
    foreignKey: 'teamGroupId',
    localKey: 'id',
  })
  public teamGroup: BelongsTo<typeof TeamGroup>
}
