import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class Package extends BaseModel {
  @column({ isPrimary: true })
  public id: number
  
  @column()
  public name: string

  @column({columnName: 'insurance_company_id'})
  public insuranceCompanyId: number

  @column()
  public price: number

  @column()
  public plafond: number

  @column()
  public taux: number

  @column()
  public validity: number

  @column()
  public status: string

  @column()
  public payment_type: string

  @column()
  public description: string

  @column()
  public settings: any

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
