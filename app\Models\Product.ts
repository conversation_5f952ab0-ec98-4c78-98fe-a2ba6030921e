import { DateTime } from 'luxon'
import { BaseModel, column, ManyToMany, manyToMany } from '@ioc:Adonis/Lucid/Orm'
// import CategoryProduct from './CategoryProduct'
import Substance from './Substance'

export default class Product extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string // Nom par defaut,fr

  @column()
  public eng_name: string | null // Nom du produit en anglais

  @column()
  public brand: string | null // classe du produit

  @column()
  public form: string | null // forme du produit

  @column()
  public dosage: string | null // Code du produit

  @column()
  public user_id: number | null

  @column({columnName: 'require_diagnostic'})
  public require_diagnostic: boolean = false

  @column()
  public description: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @manyToMany(() => Substance, {
    pivotTable: 'product_substances',
    pivotForeignKey: 'product_id',
    pivotRelatedForeignKey: 'substance_id',
    pivotTimestamps: true
  })
  public substances: ManyToMany<typeof Substance>
}
