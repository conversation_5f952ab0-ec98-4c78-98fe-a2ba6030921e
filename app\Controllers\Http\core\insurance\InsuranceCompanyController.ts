import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import AdminController from "../AdminController";
import { ApiResponse, UserStatus } from 'App/Controllers/interfaces';
import InsuranceCompany from 'App/Models/InsuranceCompany';
import Database from '@ioc:Adonis/Lucid/Database';
import User from 'App/Models/User';
import InsuranceManager from 'App/Models/InsuranceManager';
import InsuranceCompanyAgency from 'App/Models/InsuranceCompanyAgency';
import Wallet from 'App/Models/Wallet';
import Package from 'App/Models/Package';
import InsuranceCompanySubscription from 'App/Models/InsuranceCompanySubscription';
import InsuranceCompanyFee from 'App/Models/InsuranceCompanyFee';
import InsuranceCompanyBeneficiary from 'App/Models/InsuranceCompanyBeneficiary';
import { DateTime } from 'luxon';
import PatientInsuranceCompany from 'App/Models/PatientInsuranceCompany';

export default class InsuranceCompanyController extends AdminController {

  public async index({ request, response }: HttpContextContract) {

    let apiResponse: ApiResponse = {
      success: false,
      message: "User not found",
      result: null,
      errors: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const insuranceCompanies = await InsuranceCompany.query().preload('country').preload('city').paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Liste des compagnies d'assurance",
        result: insuranceCompanies,
        errors: null
      }
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: error.message,
        result: null,
        errors: null
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async addInsuranceCompany({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "User not found",
      result: null,
      errors: null
    };
    let status = 201;

    try {
      const payload = await request.validate({
        schema: schema.create({
          company: schema.object().members({
            name: schema.string({ trim: true }),
            agrement: schema.string({ trim: true }),
            email: schema.string.nullable({ trim: true }),
            phone: schema.string.nullable({ trim: true }),
            address: schema.string.nullable({ trim: true }),
            seat: schema.string.nullable({ trim: true }),
            countryId: schema.number(),
            cityId: schema.number(),
            website: schema.string.optional({ trim: true }),
            probationDuration: schema.number.nullable(),
            description: schema.string.optional({ trim: true }),
            subscription_model: schema.enum.optional(['certificats', 'plafond']),
            type: schema.enum.optional(['company', 'mutual']),
          }),
          responsable: schema.object.optional().members({
            last_name: schema.string({ trim: true }),
            first_name: schema.string({ trim: true }),
            phone: schema.string({ trim: true }),
            email: schema.string({ trim: true }),
            address: schema.string.optional({ trim: true }),
            country_id: schema.number.optional(),
            city_id: schema.number.optional(),
            quarter_id: schema.number.optional(),
            gender: schema.enum.optional(['M', 'F']),
            birthday_year: schema.number.optional(),
            birthday_month: schema.number.optional(),
            birthday_day: schema.number.optional(),
            profession: schema.string.optional({ trim: true }),
            password: schema.string.optional({ trim: true }),
          }),
          agency: schema.object.optional().members({
            name: schema.string({ trim: true }),
            is_principal: schema.boolean(),
            is_rural: schema.boolean(),
            agency_code: schema.string.nullable({ trim: true }),
            phone: schema.string.nullable({ trim: true }),
            address: schema.string.nullable({ trim: true }),
            email: schema.string.nullable({ trim: true }),
          }),
        }),
        messages: {
          'company.name.required': "Le nom de compagnie d'assurance est requis",
          'company.agrement.required': "Le n° d'agrement est requis",
          'company.countryId.required': "Le pays est requis",
          'company.cityId.required': "La ville est requis",
        }
      });

      const { company, responsable, agency } = payload;

      const trx = await Database.transaction();
      try {
        let user: User = {} as User;
        let principal_agency: InsuranceCompanyAgency = {} as InsuranceCompanyAgency;
        let manager: InsuranceManager = {} as InsuranceManager;

        if (responsable) {
          let checkExist = await User.query({ client: trx })
            .where('email', responsable.email)
            .orWhere('phone', responsable.phone)
            .first();

          if (checkExist !== null) {
            apiResponse = {
              success: false,
              message: "Un compte utilisant cette adresse email ou ce numéro de téléphone est déjà enregistré",
              result: null,
            };
            status = 400;
            return response.status(status).json(apiResponse);
          } else {
            let uuid = await this.generateUUID();
            let username = responsable.first_name + ' ' + responsable.last_name;
            user = await User.create({
              token: uuid,
              username: username,
              phone: responsable.phone,
              email: responsable.email,
              countryId: company.countryId,
              password: responsable.password,
              roleId: 14,
              status: UserStatus.Actived,
            }, { client: trx });

            if (!user) {
              await trx.rollback();
              apiResponse = {
                success: false,
                message: "Echec de création de compte utilisateur",
                result: null,
                except: "Echec de création de compte utilisateur"
              };
              status = 500;
              return response.status(status).json(apiResponse);
            }
          }
        }

        let uuid = await this.generateUUID();
        const checkInsurance = await InsuranceCompany.query({ client: trx })
          .where('name', company.name)
          .orWhere('agrement', company.agrement)
          .orWhere('phone', String(company.phone))
          .first();

        if (checkInsurance) {
          apiResponse = {
            success: false,
            message: "Une compagnie d'assurance avec ce nom ou ce n° d'agrement existe déjà",
            result: null,
            except: "Une compagnie d'assurance avec ce nom ou ce n° d'agrement existe déjà"
          };
          status = 400;
          return response.status(status).json(apiResponse);
        }

        const insurance_company = await InsuranceCompany.create({
          uuid: uuid,
          name: company.name,
          agrement: company.agrement,
          email: company.email,
          phone: company.phone,
          address: company.address,
          seat: company.seat,
          countryId: company.countryId,
          cityId: company.cityId,
          website: company.website,
          probationDuration: company.probationDuration,
          description: company.description,
          subscription_model: company.subscription_model,
          type: company.type,
        }, { client: trx });

        if (!insurance_company) {
          await trx.rollback();
          apiResponse = {
            success: false,
            message: "Echec de création de la compagnie d'assurance",
            result: null,
            except: insurance_company
          };
          status = 500;
          return response.status(status).json(apiResponse);
        }

        if (agency) {
          principal_agency = await InsuranceCompanyAgency.create({
            name: agency.name,
            isPrincipal: agency.is_principal || true,
            isRural: agency.is_rural,
            agencyCode: agency.agency_code || null,
            phone: agency.phone || null,
            address: agency.address || null,
            email: agency.email || null,
            insuranceCompanyId: insurance_company.id
          }, { client: trx });

          if (!principal_agency) {
            await trx.rollback();
            apiResponse = {
              success: false,
              message: "Echec de création de compagnie d'assurance",
              result: null,
              except: "Echec de création de compagnie d'assurance"
            };
            status = 500;
            return response.status(status).json(apiResponse);
          }
        }

        if (responsable && user) {
          manager = await InsuranceManager.create({
            user_id: user.id,
            insuranceCompanyId: insurance_company.id,
            insuranceCompanyAgencyId: principal_agency ? principal_agency.id : null,
            lastName: responsable.last_name,
            firstName: responsable.first_name,
            phone: responsable.phone,
            email: responsable.email,
            address: responsable.address || null,
            countryId: company.countryId,
            cityId: company.cityId || null,
            quarterId: responsable.quarter_id || null,
            gender: responsable.gender as 'M' | 'F' || null,
            birthday_year: responsable.birthday_year || null,
            birthday_month: responsable.birthday_month || null,
            birthday_day: responsable.birthday_day || null,
            profession: responsable.profession || null
          }, { client: trx });

          if (!manager) {
            await trx.rollback();
            apiResponse = {
              success: false,
              message: "Echec de création de responsable de compagnie d'assurance",
              result: null,
              except: "Echec de création de responsable de compagnie d'assurance"
            };
            status = 500;
            return response.status(status).json(apiResponse);
          }
        }

        if (principal_agency) {
          const agencyWalletCode = await this.generateWalletCode();
          const agencyWallet = await Wallet.create({
            ownerType: 'agency',
            ownerId: principal_agency.id,
            libelle: "WALLET " + principal_agency.name,
            typeWalletId: 2,
            code: agencyWalletCode,
          }, { client: trx });

          if (!agencyWallet) {
            await trx.rollback();
            apiResponse = {
              success: false,
              message: "Erreur lors de la création du portefeuille",
              result: null,
              except: agencyWallet
            };
            status = 500;
            return response.status(status).json(apiResponse);
          }
        }

        const walletCode = await this.generateWalletCode();
        const insuraceWallet = await Wallet.create({
          ownerType: 'insurance',
          ownerId: insurance_company.id,
          libelle: "WALLET " + insurance_company.name,
          typeWalletId: 2,
          code: walletCode,
        }, { client: trx });

        if (!insuraceWallet) {
          await trx.rollback();
          apiResponse = {
            success: false,
            message: "Erreur lors de la création du portefeuille",
            result: null,
            except: insuraceWallet
          };
          status = 500;
          return response.status(status).json(apiResponse);
        }

        await trx.commit();
        status = 201;
        apiResponse = {
          success: true,
          message: "Compagnie d'assurance ajoutée",
          result: {
            company: insurance_company,
            responsable: manager,
            principal_agency: principal_agency,
            wallet: insuraceWallet,
          },
        };
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.log("error", error.message);
        apiResponse = {
          success: false,
          message: "Echec de création de compagnie d'assurance",
          result: null,
          except: error.message
        };
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: error.message,
        result: null,
        errors: null
      };
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async getInsuranceCompanyDetails({ params, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    }
    let status = 200;
    try {
      const companyId = params.company_id;
      if (!companyId) {
        apiResponse = {
          success: false,
          message: "Veuillez fournir un identifiant de compagnie d'assurance",
          result: null,
        }
        status = 404;
        return response.status(status).json(apiResponse);
      }
      const insurance_company = await InsuranceCompany.query().where('id', companyId).orWhere('uuid', companyId).first();
      if (!insurance_company) {
        apiResponse = {
          success: false,
          message: "Compagnie d'assurance introuvable",
          result: null,
          except: "Compagnie d'assurance introuvable"
        }
        status = 404;
        return response.status(status).json(apiResponse);
      }
      status = 200;
      apiResponse = {
        success: true,
        message: "Compagnie d'assurance trouvable",
        result: insurance_company,
      }
      return response.status(status).json(apiResponse);
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: error.message,
        result: null,
        errors: null
      }
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async addInsurancePackage({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          insurance_company_id: schema.number(),
          name: schema.string(),
          price: schema.number(),
          plafond: schema.number.optional(),
          taux: schema.number(),
          validity: schema.number(),
          description: schema.string.optional(),
        })
      });
      const { insurance_company_id, name, price, plafond, taux, validity, description } = payload;
      const insurance_company = await InsuranceCompany.query().where('id', insurance_company_id).orWhere('uuid', insurance_company_id).first();
      if (!insurance_company) {
        apiResponse.message = "Compagnie d'assurance introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }
      const checkExist = await Package.query().where('name', name).where('insurance_company_id', insurance_company.id).first();
      if (checkExist) {
        apiResponse.message = "Un package avec ce nom existe déjà";
        status = 400;
        return response.status(status).json(apiResponse);
      }
      const newPackage = await Package.create({
        name: name,
        price: price,
        plafond: plafond,
        taux: taux,
        validity: validity,
        description: description,
        insuranceCompanyId: insurance_company.id,
      });
      if (!newPackage) {
        apiResponse.message = "Echec de création du package";
        apiResponse.except = newPackage;
        status = 500;
        return response.status(status).json(apiResponse);
      }
      apiResponse = {
        success: true,
        message: "Package ajouté",
        result: newPackage,
      }
      status = 201;
      return response.status(status).json(apiResponse);
    } catch (error) {
      console.log("Error in add insurance package", error.message);
      apiResponse = {
        success: false,
        message: "Echec lors de l'ajout du package",
        result: null,
        except: error.message,
        errors: error.messages
      }
      status = 500;
      response.status(status).json(apiResponse);
    }
  }

  public async getInsuranceCompanySubscriptions({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const startDate = request.input('start_date', null);
      const endDate = request.input('end_date', null);
      const orderBy = request.input('order_by', 'created_at');
      const sort = request.input('sort', 'desc');
      const insuranceCompanyId = request.input('insurance_company_id', null);

      const query = InsuranceCompanySubscription.query().orderBy(orderBy, sort)
        .preload('insurance_company')
        .preload('patient')
        .preload('package')
        .preload('patientInsurance', function (query) {
          query.preload('agency')
        })
      if (insuranceCompanyId) {
        query.where('insurance_company_id', insuranceCompanyId);
      }
      if (startDate && endDate) {
        query.whereBetween('start_date', [startDate, endDate]);
      }

      const result = await query.paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Liste des souscriptions d'assurances",
        result: result,
      }

    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: error.message,
        result: null,
        errors: null
      }
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getInsuranceCompanyFees({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    };
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const orderBy = request.input('order_by', 'id');
      const sort = request.input('sort', 'desc');
      const insuranceCompanyId = request.input('insurance_company_id', null);
      const startDate = request.input('start_date', null);
      const endDate = request.input('end_date', null);

      const query = InsuranceCompanyFee.query().orderBy(orderBy, sort).preload('insurance_company').preload('patient').preload('package').preload('agency');
      if (insuranceCompanyId) {
        query.where('insurance_company_id', insuranceCompanyId);
      }
      if (startDate && endDate) {
        query.whereBetween('start_date', [startDate, endDate]);
      }

      const result = await query.paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Liste des frais d'assurances",
        result: result,
      }
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: error.message,
        result: null,
        errors: null
      }
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async validateBeneficiary({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
      errors: null
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          insurance_company_id: schema.number(),
          beneficiary_id: schema.number(),
          code: schema.string(),
        })
      });
      const { insurance_company_id, beneficiary_id, code } = payload;
      const insurance_company = await InsuranceCompany.query().where('id', insurance_company_id).first();
      if (!insurance_company) {
        apiResponse.message = "Compagnie d'assurance introuvable";
        status = 404;
        return response.status(status).json(apiResponse);
      }
      const beneficiary = await InsuranceCompanyBeneficiary.query().where('insurance_company_id', insurance_company.id).where('id', beneficiary_id).preload('parent').forUpdate().first();
      if (!beneficiary) {
        apiResponse.message = "Ce benéficiaire n'existe pas ";
        status = 400;
        return response.status(status).json(apiResponse);
      }
      if (!beneficiary.patientId) {
        apiResponse.message = "Ce benéficiaire n'a pas de compte patient";
        status = 400;
        return response.status(status).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        const parent = beneficiary.parent;
        if (!parent) {
          apiResponse.message = "Ce bénéficiaire n'a pas de compte parent";
          status = 400;
          return response.status(status).json(apiResponse);
        }
        const parentSubscription = await InsuranceCompanySubscription.query().where('patient_id', parent.patient_id).where('insurance_company_id', parent.insurance_company_id).where('status', 'active').first();
        if (!parentSubscription) {
          apiResponse.message = "Echec de création de souscription";
          status = 500;
          return response.status(status).json(apiResponse);
        }

        let validate_at = DateTime.now();
        const patientInsurance = await PatientInsuranceCompany.create({
          patient_id: beneficiary.patientId,
          insurance_company_id: beneficiary.insuranceCompanyId,
          agencyId: parent.agencyId,
          code: code,
          status: 'validated',
          validated_at: validate_at,
          type_client: 'PAC',
          is_pac: true,
          is_active: true,
        }, { client: trx });

        if (!patientInsurance) {
          await trx.rollback();
          apiResponse.message = "Echec de création de compte patient";
          status = 500;
          return response.status(status).json(apiResponse);
        }

        const subscription = await InsuranceCompanySubscription.create({
          startDate: DateTime.now(),
          endDate: DateTime.now().plus({ years: 1 }),
          insuranceCompanyId: beneficiary.insuranceCompanyId,
          patientInsuranceCompanyId: patientInsurance.id,
          patientId: beneficiary.patientId,
          packageId: parentSubscription.packageId,
          status: 'active',
          amountPaid: parentSubscription.amountPaid,
          validatedAt: validate_at,
        }, { client: trx });

        if (!subscription) {
          await trx.rollback();
          apiResponse.message = "Echec de création de la souscription";
          status = 400;
          return response.status(status).json(apiResponse);
        }

        await beneficiary.merge({
          validatedAt: validate_at,
          status: 'validated',
        }).useTransaction(trx).save();

        await trx.commit();
        apiResponse = {
          success: true,
          message: "Le bénéficiaire a été validé",
          result: beneficiary,
        }
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.log("error in validateBeneficiary", error.message);
        status = 500;
        apiResponse = {
          success: false,
          message: "Une erreur est survenue",
          result: null,
          except: error.message
        }
      }
    } catch (error) {
      console.log("error in validateBeneficiary", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Une erreur est survenue",
        result: null,
        except: error.message
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async addSubscriptionToBeneficiary({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "Une erreur est survenue",
      result: null,
      except: null
    }
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          beneficiary_id: schema.number(),
          patient_insurance_id: schema.number(),
        })
      });
      const { beneficiary_id, patient_insurance_id } = payload;
      const beneficiary = await InsuranceCompanyBeneficiary.query().where('id', beneficiary_id).preload('parent').first();
      if (!beneficiary) {
        apiResponse.message = "Ce bénéficiaire n'existe pas";
        status = 400;
        return response.status(status).json(apiResponse);
      }
      const parent = beneficiary.parent;
      if (!parent) {
        apiResponse.message = "Ce bénéficiaire n'a pas de compte parent";
        status = 400;
        return response.status(status).json(apiResponse);
      }
      const parentSubscription = await InsuranceCompanySubscription.query().where('patient_id', parent.patient_id).where('insurance_company_id', parent.insurance_company_id).where('status', 'active').first();
      if (!parentSubscription) {
        apiResponse.message = "Echec de création de souscription";
        status = 500;
        return response.status(status).json(apiResponse);
      }
      const patientInsurance = await PatientInsuranceCompany.query().where('id', patient_insurance_id).first();
      if (!patientInsurance) {
        apiResponse.message = "Ce patient n'existe pas";
        status = 400;
        return response.status(status).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        const subscription = await InsuranceCompanySubscription.create({
          startDate: DateTime.now(),
          endDate: DateTime.now().plus({ years: 1 }),
          insuranceCompanyId: beneficiary.insuranceCompanyId,
          patientInsuranceCompanyId: patientInsurance.id,
          patientId: Number(beneficiary.patientId),
          packageId: parentSubscription.packageId,
          status: 'active',
          amountPaid: parentSubscription.amountPaid,
          validatedAt: patientInsurance.validated_at,
        }, { client: trx });

        if (!subscription) {
          await trx.rollback();
          apiResponse.message = "Echec de création de la souscription";
          status = 400;
          return response.status(status).json(apiResponse);
        }

        await trx.commit();
        apiResponse = {
          success: true,
          message: "La souscription a été ajoutée",
          result: subscription,
        }
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.log("error in add subscription to beneficiary", error.message);
        status = 500;
        apiResponse = {
          success: false,
          message: "Une erreur est survenue",
          result: null,
          except: error.message
        }
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.log("error in add subscription to beneficiary", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Une erreur est survenue",
        result: null,
        except: error.message
      }
      return response.status(status).json(apiResponse);
    }
  }

}
