// app/Models/Payment.js

import { DateTime } from "luxon"
// import PaymentGateway from "./PaymentGateway"
// import PaymentType from "./PaymentType"
// import PaymentIntent from "./PaymentIntent"
// import Patient from "./Patient"
import { BaseModel, BelongsTo, belongsTo, column, afterFetch, beforeSave } from '@ioc:Adonis/Lucid/Orm'
import PaymentIntent from "./PaymentIntent"
import PaymentType from "./PaymentType"
import Patient from "./Patient"

export class Payment extends BaseModel {
  @column({ isPrimary: true, columnName: 'id' })
  public id: number

  @column({ columnName: 'payment_gateway_id' })
  public paymentGatewayId: number

  @column({ columnName: 'trx_ref' })
  public trxRef: string

  @column({ columnName: 'payment_type_id' })
  public paymentTypeId: number

  @column({ columnName: 'payment_intent_id' })
  public paymentIntentId: number

  @column({ columnName: 'beneficiary_id' })
  public beneficiaryId: number | null

  @column({ columnName: 'beneficiary_type' })
  public beneficiaryType: 'patient' | 'laborantin' | 'pharmacien' | 'soignant' | null

  @column({ columnName: 'amount' })
  public amount: number

  @column({ columnName: 'status' })
  public status: 'pending' | 'paid' | 'cancelled' | 'failed'

  @column({ columnName: 'description' })
  public description: string | null

  @column({ columnName: 'metadata' })
  public metadata: any

  @column.dateTime({ autoCreate: false, autoUpdate: false, columnName: 'paid_at' })
  public paidAt: DateTime | null

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  @belongsTo(() => PaymentIntent, {
    foreignKey: 'paymentIntentId',
    localKey: 'id'
  })
  public paymentIntent: BelongsTo<typeof PaymentIntent>

  @belongsTo(() => PaymentType, {
    foreignKey: 'paymentTypeId',
    localKey: 'id'
  })
  public paymentType: BelongsTo<typeof PaymentType>

  @belongsTo(() => Patient, {
    foreignKey: 'beneficiaryId',
    localKey: 'id'
  })
  public patient: BelongsTo<typeof Patient>

  @afterFetch()
  public static parseMetadata(payment: Payment) {
    if (payment.metadata && typeof payment.metadata === 'string') {
      try {
        payment.metadata = JSON.parse(payment.metadata);
      } catch (error) {
        console.log("Error parsing Payment metadata:", error);
        payment.metadata = {};
      }
    }
  }

  @beforeSave()
  public static stringifyMetadata(payment: Payment) {
    if (payment.metadata && typeof payment.metadata === 'object') {
      try {
        payment.metadata = JSON.stringify(payment.metadata);
      } catch (error) {
        console.log("Error stringifying Payment metadata:", error);
        payment.metadata = '{}';
      }
    }
  }
}


