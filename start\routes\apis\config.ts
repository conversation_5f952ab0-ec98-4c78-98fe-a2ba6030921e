import Route from '@ioc:Adonis/Core/Route';
import AnalyzeController from 'App/Controllers/Http/core/configs/AnalyzeController';
import HealthInstituteController from 'App/Controllers/Http/core/configs/HealthInstituteController';
import LaboratoryController from 'App/Controllers/Http/core/configs/LaboratoryController';
import LocationController from 'App/Controllers/Http/core/configs/LocationController';
import PathologyController from 'App/Controllers/Http/core/configs/PathologyController';
import PharmacyController from 'App/Controllers/Http/core/configs/PharmacyController';
import ProductController from 'App/Controllers/Http/core/configs/ProductController';
import VitalExamController from 'App/Controllers/Http/core/configs/VitalExamController';

const analyzeCtrl = new AnalyzeController();
const healthCtrl = new HealthInstituteController();
const laboCtrl = new LaboratoryController();
const locationCtrl = new LocationController();
const pathoCtrl = new PathologyController();
const pharmaCtrl = new PharmacyController();
const prodCtrl = new ProductController();
const vitalCtrl = new VitalExamController();

Route.group(() => {
  Route.group(() => {
    Route.group(() => {
      Route.get('/', async (ctx) => {
        return analyzeCtrl.index(ctx);
      });
      Route.get('/categories', async (ctx) => {
        return analyzeCtrl.getCategories(ctx);
      });
      Route.get('/types', async (ctx) => {
        return analyzeCtrl.getAnalyzeTypes(ctx);
      });
      Route.post('/create', async (ctx) => {
        return analyzeCtrl.createAnalyze(ctx);
      });
      Route.post('/add-multiple', async (ctx) => {
        return analyzeCtrl.addMultipleAnalyze(ctx);
      });
      Route.post('/update', async (ctx) => {
        return analyzeCtrl.updateAnalyze(ctx);
      });
      Route.post('/categories/add', async (ctx) => {
        return analyzeCtrl.addCategoryAnalyze(ctx);
      });
      Route.post('/categories/update', async (ctx) => {
        return analyzeCtrl.updateCategoryAnalyze(ctx);
      });
      Route.post('/types/add', async (ctx) => {
        return analyzeCtrl.addAnalyzeType(ctx);
      });
      Route.post('/types/update', async (ctx) => {
        return analyzeCtrl.updateAnalyzeType(ctx);
      });
    }).prefix('analyzes');

    Route.group(() => {
      Route.get('/', async (ctx) => {
        return healthCtrl.getHealthInstitutes(ctx);
      });
      Route.get('/types', async (ctx) => {
        return healthCtrl.getHealthInstituteTypes(ctx);
      });
      Route.get('/managers', async (ctx) => {
        return healthCtrl.getHealthInstituteManagers(ctx);
      });
      Route.get('/doctors', async (ctx) => {
        return healthCtrl.getHealthInstitutePersonals(ctx);
      });
      Route.post('/create', async (ctx) => {
        return healthCtrl.addHealthInstitute(ctx);
      });
      Route.post('/setup', async (ctx) => {
        return healthCtrl.configHealthInstitute(ctx);
      });
      Route.post('/update', async (ctx) => {
        return healthCtrl.updateHealthInstitute(ctx);
      });
      Route.post('/types/add', async (ctx) => {
        return healthCtrl.addHealthInstituteType(ctx);
      });
      Route.post('/types/update', async (ctx) => {
        return healthCtrl.updateHealthInstituteType(ctx);
      });
      Route.post('/managers/add', async (ctx) => {
        return healthCtrl.addHealthInstituteManager(ctx);
      });
      Route.post('/managers/update', async (ctx) => {
        return healthCtrl.updateHealthInstituteManager(ctx);
      });
    }).prefix('health-institutes');

    Route.group(() => {
      Route.get('/',async(ctx) =>{
        return laboCtrl.getLaboratories(ctx);
      });
      Route.post('/create', async(ctx) => {
        return laboCtrl.createLaboratory(ctx);
      });
      Route.post('/update', async(ctx) => {
        return laboCtrl.updateLaboratory(ctx);
      });
      Route.post('/setup', async(ctx) => {
        return laboCtrl.configLaboratory(ctx);
      });
    }).prefix('laboratories');

    Route.group(() => {
      Route.get('/countries', async(ctx) => {
        return locationCtrl.getCountries(ctx);
      });
      Route.get('/cities', async(ctx) => {
        return locationCtrl.getCities(ctx);
      });
      Route.get('/districts', async(ctx) => {
        return locationCtrl.getDistricts(ctx);
      });
      Route.post('/countries/create', async(ctx) => {
        return locationCtrl.addCountry(ctx);
      });
      Route.post('/cities/create', async(ctx) => {
        return locationCtrl.addCity(ctx);
      });
      Route.post('/districts/create', async(ctx) => {
        return locationCtrl.addDistrict(ctx);
      });
    }).prefix('locations');

    Route.group(() => {
      Route.group(() => {
        Route.get('/', async(ctx) => {
          return pathoCtrl.getPathologies(ctx);
        });
        Route.get('/categories', async(ctx) => {
          return pathoCtrl.getPathologyCategories(ctx);
        });
        Route.get('/types', async(ctx) => {
          return pathoCtrl.getPathologyTypes(ctx);
        });
        Route.post('/create', async(ctx) => {
          return pathoCtrl.addPathology(ctx);
        });
        Route.post('/add-multiple', async(ctx) =>{
          return pathoCtrl.addMultiplePathology(ctx);
        })
        Route.post('/update', async(ctx) => {
          return pathoCtrl.updatePathology(ctx);
        });
        Route.post('/categories/create', async(ctx) => {
          return pathoCtrl.createPathologyCategory(ctx);
        });
        Route.post('/categories/update', async(ctx) => {
          return pathoCtrl.updatePathologyCategory(ctx);
        });
      }).prefix('pathologies');
      Route.group(() => {
        Route.get('/', async(ctx) => {
          return pathoCtrl.getDiagnosticCategories(ctx);
        });
        Route.post('/create', async(ctx) => {
          return pathoCtrl.addDiagnosticCategory(ctx);
        });
        Route.post('/update', async(ctx) => {
          return pathoCtrl.updateDiagnosticCategory(ctx);
        });
      }).prefix('category-diagnostics');
      Route.group(() => {
        Route.get('/', async(ctx) => {
          return pathoCtrl.getSymptoms(ctx);
        });
      }).prefix('symptoms');

    });

    Route.group(() => {
      Route.get('/', async(ctx) => {
        return pharmaCtrl.getPharmacies(ctx);
      });
      Route.post('/create', async(ctx) => {
        return pharmaCtrl.addPharmacy(ctx);
      });
      Route.post('/update', async(ctx) => {
        return pharmaCtrl.updatePharmacy(ctx);
      });
      Route.post('/setup', async(ctx) => {
        return pharmaCtrl.configPharmacy(ctx);
      });
    }).prefix('pharmacies');

    Route.group(() => {
      Route.get('/', async(ctx) => {
        return prodCtrl.getProducts(ctx);
      });
      Route.get('/categories', async(ctx) => {
        return prodCtrl.getCategoriesProducts(ctx);
      });
      Route.get('/types', async(ctx) => {
        return prodCtrl.getProductTypes(ctx);
      });
      Route.post('/create', async(ctx) => {
        return prodCtrl.addProduct(ctx);
      });
      Route.post('/add-multiple', async(ctx) => {
        return prodCtrl.addMultipleProduct(ctx);
      });
      Route.post('/update', async(ctx) => {
        return prodCtrl.updateProduct(ctx);
      });
      Route.post('/categories/create', async(ctx) => {
        return prodCtrl.createProductCategory(ctx);
      });
      Route.post('/categories/update', async(ctx) => {
        return prodCtrl.updateProductCategory(ctx);
      });
      Route.post('/types/create', async(ctx) => {
        return prodCtrl.createProductType(ctx);
      });
      Route.post('/types/update', async(ctx) => {
        return prodCtrl.updateProductType(ctx);
      });
    }).prefix('products');

    Route.group(() => {
      Route.get('/', async(ctx) => {
        return vitalCtrl.getVitalExams(ctx);
      });
      Route.post('/create', async(ctx) => {
        return vitalCtrl.addVitalExam(ctx);
      });
    }).prefix('vital-exams');
    Route.get('/unities', async(ctx) => {
      return vitalCtrl.getUnities(ctx);
    });

  }).prefix('configs');
}).prefix('api').namespace('App/Controllers/Http/core/configs').middleware('auth:api');
