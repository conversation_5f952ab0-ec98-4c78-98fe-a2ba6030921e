import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Team from './Team'
import Patient from './Patient'

export default class TeamMember extends BaseModel {
  @column({ isPrimary: true, columnName: 'id' })
  public id: number

  @column({ columnName: 'public_id' })
  public publicId: string

  @column({ columnName: 'team_id' })
  public teamId: number

  @column({ columnName: 'team_group_id' })
  public teamGroupId: number | null

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'role' })
  public role: 'admin' | 'hr_manager' | 'member'

  @column({ columnName: 'coverage_status' })
  public coverageStatus: 'pending' | 'active' | 'suspended' | 'terminated'

  @column({ columnName: 'employee_matricule' })
  public employeeMatricule?: string

  @column({ columnName: 'marital_status' })
  public maritalStatus?: 'single' | 'married' | 'divorced' | 'widowed'

  @column({ columnName: 'dependent_children' })
  public dependentChildren: number

  @column({ columnName: 'custom_plafond' })
  public customPlafond?: number

  @column({ columnName: 'consumption' })
  public consumption?: {
    annual_used: number
    annual_remaining: number
    last_consumption_date: string
  }

  @column({ columnName: 'create_by_self' })
  public createBySelf: boolean

  @column.date({ columnName: 'joined_at' })
  public joinedAt: DateTime

  @column.date({ columnName: 'left_at' })
  public leftAt?: DateTime

  @column.date({ columnName: 'coverage_start' })
  public coverageStart?: DateTime

  @column.date({ columnName: 'coverage_end' })
  public coverageEnd?: DateTime

  @column({ columnName: 'metadata' })
  public metadata?: any

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  @column.dateTime({ columnName: 'deleted_at' })
  public deletedAt?: DateTime

  @belongsTo(() => Team, {
    localKey: 'id',
    foreignKey: 'teamId',
  })
  public team: BelongsTo<typeof Team>

  @belongsTo(() => Patient, {
    localKey: 'id',
    foreignKey: 'patientId',
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => TeamMember, {
    localKey: 'id',
    foreignKey: 'teamId',
    onQuery(query) {
      return query.whereIn('role', ['hr_manager', 'admin'])
    }
  })
  public manager: BelongsTo<typeof TeamMember>
}
