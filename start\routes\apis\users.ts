import Route from '@ioc:Adonis/Core/Route';
import ParrainageController from 'App/Controllers/Http/core/users/ParrainageController';
import UsersController from 'App/Controllers/Http/core/users/UsersController';

const userCtrl = new UsersController();
const parrCtrl = new ParrainageController();

Route.group(() => {
  Route.group(() => {
    Route.get('/', async (ctx) => {
      return userCtrl.getUsers(ctx);
    });
    Route.get('/identities', async (ctx) => {
      return userCtrl.getUserIdentities(ctx);
    });
    Route.post('/identities/validate', async (ctx) => {
      return userCtrl.validateOwnerUserIdentity(ctx);
    });
    Route.post('/update-status', async (ctx) => {
      return userCtrl.validateAllUserStatus(ctx);
    });
    Route.post('/update-pro-wallet', async (ctx) => {
      return userCtrl.fixProUserWallet(ctx);
    })
  }).prefix('users');
  Route.group(() => {
    Route.post('/add-config', async (ctx) => {
      return parrCtrl.addParraingeConfig(ctx);
    });
    Route.post('/update', async (ctx) => {
      return parrCtrl.updateParraingeConfig(ctx);
    });
    Route.post('/validate-monetization', async (ctx) => {
      return parrCtrl.validateMonetizationRequest(ctx);
    });
    Route.post('/validate-reversal', async (ctx) => {
      return parrCtrl.validateReversalRequest(ctx);
    });
  }).prefix('parrainage');
}).prefix('api').namespace('App/Controllers/Http/core/users').middleware('auth:api');
