import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, HasOne, belongsTo, column, hasOne } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import Country from './Country'
import City from './City'
import Quarter from './Quarter'
import BloodGroup from './BloodGroup'
import HealthInstitute from './HealthInstitute'
import Code from './Code'
import Wallet from './Wallet'
import { HealthRecord } from 'App/Controllers/Utils/models'

type Address = {
  libelle?: string,
  lat?: string,
  long?: string,
}

export default class Patient extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'user_id' })
  public user_id: number

  @column({ columnName: 'last_name' })
  public last_name: string

  @column({ columnName: 'first_name' })
  public first_name: string

  @column({ columnName: 'phone' })
  public phone: string

  @column({ columnName: 'email' })
  public email: string

  @column({ columnName: 'address' })
  public address: Address[]

  @column({ columnName: 'country_id' })
  public country_id: number | null

  @column({ columnName: 'city_id' })
  public city_id: number | null

  @column({ columnName: 'quarter_id' })
  public quarter_id: number | null

  @column({ columnName: 'gender' })
  public gender: string | null

  @column({ columnName: 'birthday_year' })
  public birthday_year: number | null

  @column({ columnName: 'birthday_month' })
  public birthday_month: number | null

  @column({ columnName: 'birthday_day' })
  public birthday_day: number | null

  @column({ columnName: 'profession' })
  public profession: string | null

  @column({ columnName: 'situation_matrimoniale' })
  public situation_matrimoniale: string | null

  @column({ columnName: 'health_institute_id' })
  public health_institute_id: number | null

  @column({ columnName: 'blood_group_id' })
  public blood_group_id: number | null

  @column({ columnName: 'status' })
  public status: string | null

  @column({ columnName: 'has_assurance' })
  public has_assurance: boolean

  @column({ columnName: 'is_isme' })
  public is_isme: boolean | null

  @column({ columnName: 'is_rural' })
  public isRural: boolean | null

  @column({ columnName: 'carnet_is_active' })
  public carnet_is_active: boolean

  @column({ columnName: 'code' })
  public code: string | null

  @column({ columnName: 'emergency_contacts' ,
    prepare: (value: any) => (value ? JSON.stringify(value) : null),
    // serialize: (value: any) => value ? JSON.parse(value) : null
  })
  public emergency_contacts: any

  @column({ columnName: 'allergies'})
  public allergies: HealthRecord[] | null

  @column({ columnName: 'handicapes' })
  public handicapes: HealthRecord[] | null

  @column({ columnName: 'antecedents'})
  public antecedents: HealthRecord[] | null

  @column({ columnName: 'creator_id' })
  public creator_id: number | null

  @column({ columnName: 'creator_type' })
  public creator_type: string | null

  @column({ columnName: 'channel' })
  public channel: string | null

  @column({columnName: 'archive_quota'})
  public archive_quota: number

  @column({columnName: 'archive_quota_used'})
  public archive_quota_used: number

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  @belongsTo(() => User,{
    foreignKey: 'user_id'
  })
  public user: BelongsTo<typeof User>

  @belongsTo(() => Country, {
    foreignKey: 'country_id',
    localKey: 'id'
  })
  public country: BelongsTo<typeof Country>

  @belongsTo(() => City, {
    foreignKey: 'city_id',
    localKey: 'id'
  })
  public city: BelongsTo<typeof City>

  @belongsTo(() => Quarter, {
    foreignKey: 'quarter_id',
    localKey: 'id'
  })
  public quarter: BelongsTo<typeof Quarter>

  @belongsTo(() => BloodGroup, {
    foreignKey: 'blood_group_id',
    localKey: 'id'
  })
  public bloodGroup: BelongsTo<typeof BloodGroup>

  @belongsTo(() => HealthInstitute, {
    foreignKey: 'health_institute_id',
    localKey: 'id'
  })
  public healthInstitute: BelongsTo<typeof HealthInstitute>

  @hasOne(() => Code, {
    foreignKey: 'patientId',
    localKey: 'id'
  })
  public carnet: HasOne<typeof Code>

  @hasOne(() => Wallet, {
    foreignKey: 'ownerId',
    onQuery: (query) => {
      query.where('owner_type', 'patient'); // Filtrer les Wallets liés à ce marchand
    },
  })
  public wallet: HasOne<typeof Wallet>

}
