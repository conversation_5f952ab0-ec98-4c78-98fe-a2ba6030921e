import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import InsuranceCompany from './InsuranceCompany'
import Soignant from './Soignant'
import { schema } from '@ioc:Adonis/Core/Validator';

export default class InsuranceMedicalAdvisor extends BaseModel {
  @column({ isPrimary: true, columnName: 'id' })
  public id: number

  @column({ columnName: 'uuid' })
  public uuid: string

  @column({ columnName: 'code' })
  public code: string

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column({ columnName: 'soignant_id' })
  public soignantId: number

  @column({ columnName: 'role' })
  public role: 'validator' | 'reviewer' | 'chief' = 'validator'

  @column({ columnName: 'permissions' })
  public permissions: object | null

  @column({ columnName: 'status' })
  public status: 'pending' | 'active' | 'inactive' | 'blocked' | 'on_leave' = 'pending'

  @column({ columnName: 'can_validate_prescriptions' })
  public canValidatePrescriptions: boolean = true

  @column({ columnName: 'can_validate_analyzes' })
  public canValidateAnalyzes: boolean = true

  @column({ columnName: 'max_items_per_day' })
  public maxItemsPerDay: number | null

  @column.dateTime({ columnName: 'activated_at' })
  public activatedAt: DateTime | null

  @column.dateTime({ columnName: 'blocked_at' })
  public blockedAt: DateTime | null

  @column.dateTime({ columnName: 'last_review_at' })
  public lastReviewAt: DateTime | null

  @column.dateTime({ columnName: 'created_at', autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ columnName: 'updated_at', autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => InsuranceCompany, {
    foreignKey: 'insuranceCompanyId',
    localKey: 'id',
  })
  public insurance_company: BelongsTo<typeof InsuranceCompany>
  @belongsTo(() => Soignant, {
    foreignKey: 'soignantId',
    localKey: 'id',
  })
  public soignant: BelongsTo<typeof Soignant>

  public static schemaCreate() {
    return schema.create({
      code: schema.string(),
      insurance_company_id: schema.number(),
      soignant_id: schema.number(),
      role: schema.string(),
      status: schema.string(),
      can_validate_prescriptions: schema.boolean(),
      can_validate_analyzes: schema.boolean(),
    })
  }
}
