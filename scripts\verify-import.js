const axios = require('axios');

/**
 * Script pour vérifier les données importées dans les 4 tables
 */

async function verifyImport() {
  console.log('🔍 Vérification des données importées...');
  
  try {
    const baseURL = 'http://localhost:3333';
    
    // Vérifier les données via des requêtes SQL directes ou des endpoints
    console.log('📊 Statistiques d\'importation:');
    console.log('');
    
    // Note: Ces endpoints n'existent peut-être pas encore, c'est juste pour illustration
    const tables = [
      { name: 'guarantee_types', description: 'Spécialités (tables existantes)' },
      { name: 'guarantees', description: 'Actes (tables existantes)' },
      { name: 'specialities', description: 'Spécialités (nouvelles tables)' },
      { name: 'acts', description: 'Actes (nouvelles tables)' }
    ];
    
    for (const table of tables) {
      try {
        // Ici vous pourriez faire des requêtes pour compter les enregistrements
        console.log(`✓ ${table.name}: ${table.description}`);
        // const count = await getTableCount(table.name);
        // console.log(`   Nombre d'enregistrements: ${count}`);
      } catch (error) {
        console.log(`❌ ${table.name}: Erreur lors de la vérification`);
      }
    }
    
    console.log('');
    console.log('💡 Pour vérifier manuellement les données:');
    console.log('   1. Connectez-vous à votre base de données');
    console.log('   2. Exécutez les requêtes suivantes:');
    console.log('      SELECT COUNT(*) FROM guarantee_types;');
    console.log('      SELECT COUNT(*) FROM guarantees;');
    console.log('      SELECT COUNT(*) FROM specialities;');
    console.log('      SELECT COUNT(*) FROM acts;');
    console.log('');
    console.log('   3. Vérifiez quelques exemples:');
    console.log('      SELECT * FROM guarantee_types LIMIT 5;');
    console.log('      SELECT * FROM guarantees LIMIT 5;');
    console.log('      SELECT * FROM specialities LIMIT 5;');
    console.log('      SELECT * FROM acts LIMIT 5;');
    
  } catch (error) {
    console.error('❌ Erreur lors de la vérification:', error.message);
  }
}

// Fonction utilitaire pour compter les enregistrements (à implémenter selon vos besoins)
async function getTableCount(tableName) {
  // Ici vous pourriez implémenter une requête pour compter les enregistrements
  // Par exemple, via un endpoint dédié ou une connexion directe à la DB
  return 0;
}

// Exécuter la vérification
if (require.main === module) {
  verifyImport();
}

module.exports = { verifyImport };
