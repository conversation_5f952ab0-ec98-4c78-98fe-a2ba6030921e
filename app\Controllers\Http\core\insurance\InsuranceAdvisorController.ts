import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import HelperController from '../../helpers/HelperController';
import { ApiResponse, UserStatus } from 'App/Controllers/interfaces';
import InsuranceMedicalAdvisor from 'App/Models/InsuranceMedicalAdvisor';
import { schema } from '@ioc:Adonis/Core/Validator';
import Soignant from 'App/Models/Soignant';
import User from 'App/Models/User';
import { DateTime } from 'luxon';
import Database from '@ioc:Adonis/Lucid/Database';

export default class InsuranceAdvisorController extends HelperController {

  public async index({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      let page = request.input('page', 1);
      let limit = request.input('limit', 10);
      let insuranceCompanyId = request.input('insurance_company_id', null);

      let query = InsuranceMedicalAdvisor.query().preload('insurance_company').preload('soignant');
      if (insuranceCompanyId) {
        query.where('insurance_company_id', insuranceCompanyId);
      }
      let advisors = await query.paginate(page, limit);
      apiResponse.success = true;
      apiResponse.message = "Liste des conseillers";
      apiResponse.result = advisors;
    } catch (error) {
      apiResponse.message = "Une erreur est survenue";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async createInsuranceAdvisor({ request, response, auth }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          insurance_company_id: schema.number(),
          soignant_id: schema.number.optional(),
          role: schema.string(),
          is_existing: schema.boolean.optional(),
          advisor: schema.object.optional().members({
            first_name: schema.string(),
            last_name: schema.string(),
            email: schema.string(),
            phone: schema.string(),
            country_id: schema.number(),
            city_id: schema.number.optional(),
            address: schema.string.optional(),
            gender: schema.enum(['M', 'F']),
            profession: schema.string.optional(),
            birthday_year: schema.number.nullable(),
            birthday_month: schema.number.nullable(),
            birthday_day: schema.number.nullable(),
            password: schema.string(),
          })
        }),
        messages: {}
      });

      const { insurance_company_id, soignant_id, role, advisor, is_existing } = payload;

      let creatorId = auth.user?.id;
      const trx = await Database.transaction();
      try {
        let soignant: Soignant | null = null;
        let soignantId = soignant_id;
        if (!is_existing && advisor) {
          //check if soignant already exists
          const checkUser = await User.query().where('email', advisor.email).orWhere('phone',advisor.phone).first();
          if (checkUser) {
            await trx.rollback();
            apiResponse.message = "Une erreur est survenue";
            apiResponse.except = "Un compte utilisateur existe déjà avec cet email ou ce numéro de téléphone";
            status = 500;
            return response.status(status).json(apiResponse);
          }
          //create soignant account if not existing
          let username = advisor?.last_name + advisor?.first_name;
          let codeP = await this.generateCodeParrainage(8);
          const parrainage = {
            create_account: 0,
            active_qrcode: 0,
            adhesion_fees: 0,
            plan: 1,
            activeMoney: false
          }
          const user = await User.create({
            username: username,
            email: advisor.email,
            phone: advisor.phone,
            password: advisor.password,
            countryId: advisor.country_id,
            languageId: 1,
            roleId: 3,
            creatorId: creatorId,
            status: UserStatus.Actived,
            activatedAt: DateTime.now(),
            codeParrainage: codeP,
            parrainage: JSON.stringify(parrainage),
          },{client: trx});

          if (!user) {
            await trx.rollback();
            apiResponse.message = "Une erreur est survenue";
            apiResponse.except = "Impossible de créer le compte de l'utilisateur";
            status = 500;
            return response.status(status).json(apiResponse);
          }
          const code = await this.generateUUID();
          soignant = await Soignant.create({
            userId: user.id,
            firstName: advisor.first_name,
            lastName: advisor.last_name,
            gender: advisor.gender,
            email: advisor.email,
            phone: advisor.phone,
            birthdayYear: advisor.birthday_year ?? null,
            birthdayMonth: advisor.birthday_month ?? null,
            birthdayDay: advisor.birthday_day ?? null,
            address: advisor.address ?? null,
            profession: advisor.profession ?? null,
            cityId: advisor.city_id,
            isAdvisor: true,
            code: code,
            status: 'activated',
          },{client: trx});

          if (!soignant) {
            await trx.rollback();
            apiResponse.message = "Une erreur est survenue";
            apiResponse.except = "Impossible de créer le compte soignant";
            status = 500;
            return response.status(status).json(apiResponse);
          }
          soignantId = soignant.id;
        }else if(soignant_id){
          soignant = await Soignant.query().where('id', soignant_id).first();
          if (!soignant) {
            await trx.rollback();
            apiResponse.message = "Compte soignant introuvable";
            apiResponse.except = "Impossible de trouver le soignant";
            status = 500;
            return response.status(status).json(apiResponse);
          }
          await soignant.merge({
            isAdvisor: true,
          }).useTransaction(trx).save();
        }

        //create insurance medical advisor
        let uuid = await this.generateUUID();
        let code = soignant?.lastName.substring(0,3).toUpperCase() + '-'+ Math.floor(Math.random() * 10000);
        const insuranceAdvisor = await InsuranceMedicalAdvisor.create({
          insuranceCompanyId: insurance_company_id,
          soignantId: soignantId,
          role: role as 'validator' | 'reviewer' | 'chief',
          status: 'active',
          canValidatePrescriptions: true,
          code: code,
          uuid: uuid,
          activatedAt: DateTime.now(),
        },{client: trx});
        if (!insuranceAdvisor) {
          await trx.rollback();
          apiResponse.message = "Une erreur est survenue";
          apiResponse.except = "Impossible de créer le conseiller";
          status = 500;
          return response.status(status).json(apiResponse);
        }

        //send notification after
        await trx.commit();
        apiResponse.success = true;
        apiResponse.message = `Le personnel soignant ${soignant?.lastName} ${soignant?.firstName} } a été assigné comme medecin conseiller `;
        apiResponse.result = {
          insurance_advisor: insuranceAdvisor,
          soignant: soignant,
        };
        return response.status(status).json(apiResponse);

      } catch (error) {
        await trx.rollback();
        console.log('============error in db========================');
        console.log(error);
        console.log('====================================');
        apiResponse.message = "Une erreur est survenue";
        apiResponse.except = error.message;
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.log('====================================');
      console.log(error);
      console.log('====================================');
      apiResponse.message = "Une erreur est survenue";
      apiResponse.except = error.message;
      apiResponse.errors = error.messages;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async updateInsuranceAdvisor({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          insurance_advisor_id: schema.number(),
          status: schema.enum.optional(['pending', 'active', 'inactive', 'blocked', 'on_leave'] as const),
          can_validate_prescriptions: schema.boolean.optional(),
          can_validate_analyzes: schema.boolean.optional(),
        }),
        messages: {}
      });

      const { insurance_advisor_id, status: advisor_status, can_validate_prescriptions, can_validate_analyzes } = payload;
      const insuranceAdvisor = await InsuranceMedicalAdvisor.query().where('id', insurance_advisor_id).first();
      if (!insuranceAdvisor) {
        apiResponse.message = "Conseiller introuvable";
        apiResponse.except = "Impossible de trouver le conseiller";
        status = 404;
        return response.status(status).json(apiResponse);
      }

      await insuranceAdvisor.merge({
        status: advisor_status,
        canValidatePrescriptions: can_validate_prescriptions,
        canValidateAnalyzes: can_validate_analyzes,
        blockedAt: advisor_status === 'blocked' ? DateTime.now() : null,
      }).save();

      let message = '';
      if (advisor_status === 'blocked') {
        message = `Le conseiller ${insuranceAdvisor.soignant.lastName} ${insuranceAdvisor.soignant.firstName} a été bloqué`;
      } else if (advisor_status === 'active') {
        message = `Le conseiller ${insuranceAdvisor.soignant.lastName} ${insuranceAdvisor.soignant.firstName} a été réactivé`;
      } else if (advisor_status === 'inactive') {
        message = `Le conseiller ${insuranceAdvisor.soignant.lastName} ${insuranceAdvisor.soignant.firstName} a été désactivé`;
      } else if (advisor_status === 'on_leave') {
        message = `Le conseiller ${insuranceAdvisor.soignant.lastName} ${insuranceAdvisor.soignant.firstName} a quitté l'entreprise`;
      }

      apiResponse.success = true;
      apiResponse.message = message;
      apiResponse.result = insuranceAdvisor;
    } catch (error) {
      apiResponse.message = "Une erreur est survenue";
      apiResponse.except = error.message;
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }
}
