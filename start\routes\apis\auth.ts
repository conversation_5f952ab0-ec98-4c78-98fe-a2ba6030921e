import Hash from '@ioc:Adonis/Core/Hash'
import Route from '@ioc:Adonis/Core/Route';
import AuthController from 'App/Controllers/Http/auth/AuthController';
import User from 'App/Models/User';

const authCtrl = new AuthController();

Route.group(() => {

  Route.group(() => {
    Route.post('/login', async (ctx) => {
      return authCtrl.login(ctx);
    });

    Route.post('/logout', async (ctx) => {
      return authCtrl.logout(ctx);
    }).middleware('auth:api');

  }).prefix('auth');

  Route.post('/update-password', async ({ request, response }) => {
    const password = request.input('password');
    const userId = request.input('user_id');

    const user = await User.query().where('id', userId).first();
    if (!user) {
      return response.status(404).json({
        success: false,
        message: "User not found",
        result: null,
        errors: null
      });
    }

    user.password = password;
    await user.save();

    return response.status(200).json({
      success: true,
      message: "Password updated",
      result: user,
    });
  });

  Route.post('/set-pro-password', async ({ response }) => {
    try {
      // Récupérer tous les pharmaciens et laborantins en une seule requête
      const usersToUpdate = await User.query()
        .whereIn('roleId', [4, 5])
        .select('id');
  
      // Mettre à jour les mots de passe en une seule opération
      const password = await Hash.make("1234");
      await User.query()
        .whereIn('id', usersToUpdate.map(user => user.id))
        .update({ password: password });
  
      return response.status(200).json({
        success: true,
        message: "Password updated",
        result: null,
      });
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: "An error occurred while updating passwords",
        error: error.message,
      });
    }
  });

}).prefix('api').namespace('App/Controllers/Http/auth');
