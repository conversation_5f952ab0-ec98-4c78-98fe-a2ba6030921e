import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext';
import { ApiResponse } from "App/Controllers/interfaces";
import FileReader from "../../helpers/FileReader";
import Database from '@ioc:Adonis/Lucid/Database';
import CategoryProduct from 'App/Models/CategoryProduct';
import Symptom from 'App/Models/Symptom';
import Substance from 'App/Models/Substance';
import Pathology from 'App/Models/Pathology';
import PathologyType from 'App/Models/PathologyType';
import PathologyCategory from 'App/Models/PathologyCategory';
import Product from 'App/Models/Product';
import ProductSubstance from 'App/Models/ProductSubstance';
import CategoryAnalyze from 'App/Models/CategoryAnalyze';
import AnalyzeType from 'App/Models/AnalyzeType';
import Analyze from 'App/Models/Analyze';
import GuaranteeType from 'App/Models/GuaranteeType';
import Guarantee from 'App/Models/Guarantee';
import Speciality from 'App/Models/Speciality';
import Act from 'App/Models/Act';

export type PathologyData = {
  pathologyTypeId: number;
  tempKey: string;
  tempParentKey: string | null;
  isLeaf: boolean;
  level: number;
  name: string;
  engName: string | null;
  code: string | null;
  description: string | null;
}

export type typeToInsert = {
  name: string;
  is_parent: boolean;
  parent_id: number | null;
  category_id: number;
  description: string | null;
  slug: string;
}

export type analyzeToInsert = {
  name: string;
  analyse_type_id: string;
}

export default class ImportMedicalDataController {

  private getParentCode(code: string | null): string | null {
    if (!code?.trim()) return null;

    const parts = code.split('.');

    // Cas des codes avec points (A00.1 → A00)
    if (parts.length > 1) return parts.slice(0, -1).join('.');

    return null;
  }

  public async clearTable(tableName: string) {
    try {
      await Database.rawQuery('SET FOREIGN_KEY_CHECKS=0').exec()
      await Database.rawQuery(`TRUNCATE TABLE ${tableName}`).exec()
      await Database.rawQuery('SET FOREIGN_KEY_CHECKS=1').exec()
      return true
    } catch (error) {
      console.error(`Erreur clearTable: ${error.message}`)
      return false
    }
  }

  public async makeImportProductCategory({ response }: HttpContextContract) {
    const apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    }

    try {
      const categories = await FileReader.readClassificationAct();
      const trx = await Database.transaction();
      //truncate table
      await this.clearTable('category_products');
      try {
        // Préparation des données
        const categoriesToInsert = categories.flatMap(category => {
          category.level_name_fr = category.level_name_fr.replace(/&amp;/g, '&');
          const level = {
            code: category.level,
            eng_name: category.level_name,
            name_fr: category.level_name_fr,
          };

          return category.classes.map(classe => {
            classe.class_name_fr = classe.class_name_fr.replace(/&amp;/g, '&');

            return {
              classCode: classe.class_code,
              name: classe.class_name_fr,
              engName: classe.class_name,
              level: JSON.stringify(level),
            };
          });
        });

        const createdCategories = await CategoryProduct.createMany(categoriesToInsert, { client: trx });
        if (!createdCategories) {
          await trx.rollback();
          console.error("Erreur lors de l'insertion des catégories");

          Object.assign(apiResponse, {
            message: "Erreur lors de l'insertion des catégories",
            except: createdCategories,
          });

          return response.status(400).json(apiResponse);
        }

        await trx.commit();

        Object.assign(apiResponse, {
          success: true,
          message: `${categoriesToInsert.length} catégories importées avec succès`,
          result: createdCategories,
        });

        return response.status(201).json(apiResponse);

      } catch (error) {
        await trx.rollback();
        console.error("Erreur lors de l'import:", error);

        Object.assign(apiResponse, {
          message: "Erreur lors de l'import des données",
          except: error.message,
        });

        return response.status(500).json(apiResponse);
      }

    } catch (error) {
      console.error("Erreur lors de la lecture du fichier:", error);

      Object.assign(apiResponse, {
        message: "Echec de récupération des données",
        except: error.message
      });

      return response.status(500).json(apiResponse);
    }
  }

  public async makeImportSymptoms({ response }: HttpContextContract) {
    const apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    }

    try {
      const trx = await Database.transaction();
      const symptoms = await FileReader.readSymptoms();
      console.log("symptoms is loaded", symptoms.length);

      //truncate table
      await this.clearTable('symptoms');
      try {
        const symptomsToInsert = symptoms.flatMap(chapter => {
          return chapter.blocks.flatMap(block => {
            return block.categories.flatMap(category => {
              return category.sub_categories.map(subCategory => {
                // Construction de l'objet hiérarchique
                const hierarchy = {
                  chapter: {
                    id: chapter.id,
                    name_fr: chapter.name_fr,
                    name_en: chapter.name_en
                  },
                  block: {
                    id: block.id,
                    name_fr: block.name_fr,
                    name_en: block.name_en
                  },
                  category: {
                    id: category.id,
                    name_fr: category.name_fr,
                    name_en: category.name_en
                  }
                };

                return {
                  code: subCategory.code,
                  nameFr: `${subCategory.name_fr.replace('-', '')}`,
                  nameEn: `${subCategory.name_en.replace('-', '')}`,
                  parentCode: this.getParentCode(subCategory.code),
                  hierarchy: JSON.stringify(hierarchy),
                  description: null,
                };
              });
            });
          });
        });

        // Insertion en masse
        const createdSymptoms = await Symptom.createMany(symptomsToInsert, { client: trx });
        if (!createdSymptoms) {
          await trx.rollback();
          console.error("Erreur lors de l'insertion des symptômes");

          apiResponse.message = "Erreur lors de l'insertion des symptômes";
          apiResponse.except = createdSymptoms;

          return response.status(400).json(apiResponse);
        }

        await trx.commit();

        apiResponse.success = true;
        apiResponse.message = `${symptomsToInsert.length} symptômes importés avec succès`;
        apiResponse.result = createdSymptoms;

        return response.status(201).json(apiResponse);

      } catch (error) {
        await trx.rollback();
        console.error("Erreur lors de l'import:", error);

        apiResponse.message = "Erreur lors de l'import des données";
        apiResponse.except = error.message;
        return response.status(500).json(apiResponse);
      }

    } catch (error) {
      console.error("Erreur lors de la lecture du fichier:", error);

      apiResponse.message = "Echec de récupération des données";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async makeImportDCI({ response }: HttpContextContract) {
    const apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    }

    try {
      const substances = await FileReader.readDCI();
      const trx = await Database.transaction();
      //truncate table
      await this.clearTable('substances');
      try {

        const substancesToInsert = substances.map(substance => {
          return {
            name: substance.DCI,
            engName: null,
            description: null,
          };
        });

        const createdSubstances = await Substance.createMany(substancesToInsert, { client: trx });
        if (!createdSubstances) {
          await trx.rollback();
          console.error("Erreur lors de l'insertion des substances");

          apiResponse.message = "Erreur lors de l'insertion des substances";
          apiResponse.except = createdSubstances;

          return response.status(400).json(apiResponse);
        }
        await trx.commit();
        apiResponse.success = true;
        apiResponse.message = `${substancesToInsert.length} substances importées avec succès`;
        apiResponse.result = createdSubstances;

        return response.status(201).json(apiResponse);

      } catch (error) {
        await trx.rollback();
        console.error("Erreur lors de l'import:", error);

        apiResponse.message = "Erreur lors de l'import des données";
        apiResponse.except = error.message;
        return response.status(500).json(apiResponse);
      }
    } catch (error) {
      console.error("Erreur lors de la lecture du fichier:", error);

      apiResponse.message = "Echec de récupération des données";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async makeImportPathologies({ response }: HttpContextContract) {
    const apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };

    try {
      const pathologies = await FileReader.readPathologies();
      const trx = await Database.transaction();
      //truncate table
      await this.clearTable('pathology_categories');
      await this.clearTable('pathology_types');
      await this.clearTable('pathologies');

      try {
        // 1. Création des PathologyCategory (chapitres)
        const categories = await PathologyCategory.createMany(
          pathologies.map(chapter => ({
            code: chapter.id,
            name: chapter.name_fr,
            engName: chapter.name_en,
            description: null,
          })),
          { client: trx }
        );

        // 2. Création des PathologyType (blocs)
        const types = await PathologyType.createMany(
          pathologies.flatMap(chapter =>
            chapter.blocks.map(block => ({
              pathologyCategoryId: categories.find(c => c.code === chapter.id)!.id,
              code: block.id.toString(),
              name: block.name_fr,
              engName: block.name_en,
              isLeaf: false,
              description: null,
            }))
          ),
          { client: trx }
        );

        // 3. Collecte de tous les codes pour détection des parents
        const allCodes = new Set<string>();
        pathologies.forEach(chapter =>
          chapter.blocks.forEach(block =>
            block.categories.forEach(category =>
              category.sub_categories.forEach(subCat => {
                if (subCat.code) allCodes.add(subCat.code);
              })
            )
          )
        );

        // 4. Préparation des données
        const pathologiesToCreate: PathologyData[] = [];

        for (const chapter of pathologies) {
          for (const block of chapter.blocks) {
            const parentType = types.find(t => t.code === block.id.toString())!;

            for (const category of block.categories) {
              // Niveau 1: Catégorie principale
              const categoryKey = `cat_${category.id}`;
              pathologiesToCreate.push({
                pathologyTypeId: parentType.id,
                tempKey: categoryKey,
                tempParentKey: null,
                isLeaf: category.sub_categories.length === 0,
                level: 1,
                name: category.name_fr.replace(/^-+\s*/, '').trim(),
                engName: category.name_en.replace(/^-+\s*/, '').trim(),
                code: null,
                description: null,
              });

              // Niveau 2: Sous-catégories
              for (const subCategory of category.sub_categories) {
                const hasChildren = Array.from(allCodes).some(
                  code => code !== subCategory.code && code.startsWith(`${subCategory.code}.`)
                );

                const isLevel2 = !subCategory.code.includes('.');
                if (!isLevel2) continue;

                pathologiesToCreate.push({
                  pathologyTypeId: parentType.id,
                  tempKey: subCategory.code,
                  tempParentKey: categoryKey,
                  isLeaf: !hasChildren,
                  level: 2,
                  name: subCategory.name_fr.replace(/^-+\s*/, '').trim(),
                  engName: subCategory.name_en.replace(/^-+\s*/, '').trim(),
                  code: subCategory.code,
                  description: null,
                });

                // Niveau 3: Items
                if (hasChildren) {
                  const childItems = category.sub_categories.filter(
                    item => item.code.startsWith(`${subCategory.code}.`)
                  );

                  for (const item of childItems) {
                    pathologiesToCreate.push({
                      pathologyTypeId: parentType.id,
                      tempKey: item.code,
                      tempParentKey: subCategory.code,
                      isLeaf: true,
                      level: 3,
                      name: item.name_fr.replace(/^-+\s*/, '').trim(), // Nom spécifique à l'item
                      engName: item.name_en.replace(/^-+\s*/, '').trim(), // Nom spécifique à l'item
                      code: item.code,
                      description: null,
                    });
                  }
                }
              }
            }
          }
        }

        // 5. Insertion initiale
        const createdPathologies = await Pathology.createMany(
          pathologiesToCreate.map(({ tempKey, tempParentKey, ...rest }) => rest),
          { client: trx }
        );

        // 6. Mise à jour des relations
        const keyToIdMap = new Map<string, number>();
        createdPathologies.forEach((pathology, index) => {
          const tempKey = pathologiesToCreate[index].tempKey;
          if (tempKey) keyToIdMap.set(tempKey, pathology.id);
        });

        await Promise.all(
          createdPathologies.map(async (pathology, index) => {
            const tempParentKey = pathologiesToCreate[index].tempParentKey;
            if (tempParentKey && pathology.level === 3) {
              pathology.parentId = keyToIdMap.get(tempParentKey) || null;
              await pathology.useTransaction(trx).save();
            }
          })
        );

        await trx.commit();

        apiResponse.success = true;
        apiResponse.message = "Données médicales importées avec succès";
        apiResponse.result = {
          categories: categories.length,
          types: types.length,
          pathologies: createdPathologies.length,
        };

        return response.status(201).json(apiResponse);

      } catch (error) {
        await trx.rollback();
        console.error("Erreur lors de l'import:", error);

        apiResponse.message = "Erreur lors de l'import des données";
        apiResponse.except = error.message;
        return response.status(500).json(apiResponse);
      }

    } catch (error) {
      console.error("Erreur:", error);

      apiResponse.message = "Échec de la récupération des données";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async makeImportProducts({ response }: HttpContextContract) {
    const apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };

    try {
      const products = await FileReader.readInamProducts();
      const trx = await Database.transaction();

      // Vidage des tables
      await this.clearTable('product_substances');
      await this.clearTable('products');

      try {
        // 1. Préparation des produits à insérer
        const productsToInsert = products.filter(product => product.commercial_name && product.commercial_name.trim() !== '').map(product => ({
          name: product.commercial_name,
          eng_name: null,
          description: null,
          brand: product.Type,
          form: product.Forme,
          dosage: product.Dosage,
          require_diagnostic: true,
        }));

        // 2. Insertion des produits
        const createdProducts = await Product.createMany(productsToInsert, { client: trx });

        // 3. Traitement des substances sans doublons
        const uniqueSubstancePairs = new Set<string>();
        const productSubstancesToInsert: { productId: number, substanceId: number }[] = [];

        for (const product of products) {
          const createdProduct = createdProducts.find(p => p.name === product.commercial_name);
          if (!createdProduct) continue;

          // Extraction des IDs de substances
          const substanceIds = product.dci_id
            ? product.dci_id.split(',').map(id => id.trim().replace('DCI-', ''))
            : [];

          for (const substanceId of substanceIds) {
            if (!substanceId) continue;

            const pairKey = `${createdProduct.id}-${substanceId}`;

            // Vérification des doublons avant ajout
            if (!uniqueSubstancePairs.has(pairKey)) {
              uniqueSubstancePairs.add(pairKey);
              productSubstancesToInsert.push({
                productId: createdProduct.id,
                substanceId: parseInt(substanceId),
              });
            }
          }
        }

        // 4. Insertion des relations uniques
        await ProductSubstance.createMany(productSubstancesToInsert, { client: trx });

        await trx.commit();

        apiResponse.success = true;
        apiResponse.message = `${createdProducts.length} produits importés avec ${productSubstancesToInsert.length} relations substances`;
        apiResponse.result = {
          products: createdProducts.length,
          substances: productSubstancesToInsert.length,
        };

        return response.status(201).json(apiResponse);

      } catch (error) {
        await trx.rollback();
        console.error("Erreur lors de l'import:", error);

        apiResponse.message = "Erreur lors de l'import des données";
        apiResponse.except = error.message;
        return response.status(500).json(apiResponse);
      }

    } catch (error) {
      console.error("Erreur:", error);

      apiResponse.message = "Échec de la récupération des données";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async makeImportAnalyzes({ response }: HttpContextContract) {
    const apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };

    try {
      const analyzes = await FileReader.readAnalyzes();
      const trx = await Database.transaction();

      // Vidage des tables
      await this.clearTable('analyzes');
      await this.clearTable('analyse_types');
      await this.clearTable('category_analyzes');

      try {
        // 1. Création des catégories
        const createdCategories = await CategoryAnalyze.createMany(
          analyzes.categories.map(category => ({
            libelle: category.name,
            description: null,
          })),
          { client: trx }
        );

        if (!createdCategories || createdCategories.length === 0) {
          await trx.rollback();
          apiResponse.message = "Aucune catégorie créée";
          return response.status(400).json(apiResponse);
        }

        // 2. Préparation des types et sous-types
        const typesToCreate: Array<{
          name: string;
          is_parent: boolean;
          parent_id: string | null; // Temporairement string pour le mapping
          category_id: number;
          description: string | null;
          slug: string;
          tempKey?: string;
        }> = [];

        const analyzesToCreate: Array<{
          name: string;
          analyse_type_id: string; // Temporairement string pour le mapping
        }> = [];

        for (const category of analyzes.categories) {
          const categoryId = createdCategories.find(c => c.libelle === category.name)?.id;
          if (!categoryId) continue;

          for (const type of category.types) {
            // Type parent (si pas de sous-types)
            if (!type.subTypes || type.subTypes.length === 0) {
              const typeKey = `type_${category.name}_${type.name}`;
              typesToCreate.push({
                name: type.name,
                is_parent: true,
                parent_id: null,
                category_id: categoryId,
                description: null,
                slug: type.name.toLowerCase().replace(/\s+/g, '-'),
                tempKey: typeKey
              });

              // Analyses directes du type
              if (type.analyses && type.analyses.length > 0) {
                analyzesToCreate.push(...type.analyses.map(analyze => ({
                  name: analyze.name,
                  analyse_type_id: typeKey,
                })));
              }
            }
            // Types avec sous-types
            else {
              // Type parent
              const parentTypeKey = `parent_type_${category.name}_${type.name}`;
              typesToCreate.push({
                name: type.name,
                is_parent: true,
                parent_id: null,
                category_id: categoryId,
                description: null,
                slug: type.name.toLowerCase().replace(/\s+/g, '-'),
                tempKey: parentTypeKey
              });

              // Sous-types
              for (const subType of type.subTypes) {
                const subTypeKey = `subtype_${category.name}_${type.name}_${subType.name}`;
                typesToCreate.push({
                  name: subType.name,
                  is_parent: false,
                  parent_id: parentTypeKey,
                  category_id: categoryId,
                  description: null,
                  slug: subType.name.toLowerCase().replace(/\s+/g, '-'),
                  tempKey: subTypeKey
                });

                // Analyses des sous-types
                if (subType.analyses && subType.analyses.length > 0) {
                  analyzesToCreate.push(...subType.analyses.map(analyze => ({
                    name: analyze.name,
                    analyse_type_id: subTypeKey,
                  })));
                }
              }
            }
          }
        }

        // 3. Insertion des types et sous-types
        const createdTypes = await AnalyzeType.createMany(
          typesToCreate.map(({ parent_id, tempKey, ...rest }) => rest),
          { client: trx }
        );

        // 4. Création du mapping des clés temporaires vers les IDs
        const typeKeyToId = new Map<string, number>();
        typesToCreate.forEach((type, index) => {
          if (type.tempKey) {
            typeKeyToId.set(type.tempKey, createdTypes[index].id);
          }
        });

        // 5. Mise à jour des parent_id pour les sous-types
        await Promise.all(
          createdTypes.map(async (type, index) => {
            const originalType = typesToCreate[index];
            if (originalType.parent_id && originalType.tempKey) {
              type.parent_id = typeKeyToId.get(originalType.parent_id) || null;
              await type.useTransaction(trx).save();
            }
          })
        );

        // 6. Insertion des analyses avec les bons IDs
        const createdAnalyzes = await Analyze.createMany(
          analyzesToCreate.map(analyze => ({
            name: analyze.name,
            analyse_type_id: typeKeyToId.get(analyze.analyse_type_id) || null,
          })),
          { client: trx }
        );

        await trx.commit();

        apiResponse.success = true;
        apiResponse.message = `Import réussi :
                    ${createdCategories.length} catégories,
                    ${createdTypes.length} types,
                    ${createdAnalyzes.length} analyses`;
        apiResponse.result = {
          categories: createdCategories.length,
          types: createdTypes.length,
          analyzes: createdAnalyzes.length,
        };

        return response.status(201).json(apiResponse);

      } catch (error) {
        await trx.rollback();
        console.error("Erreur lors de l'import:", error);

        apiResponse.message = "Erreur lors de l'import des données";
        apiResponse.except = error.message;
        return response.status(500).json(apiResponse);
      }

    } catch (error) {
      console.error("Erreur:", error);

      apiResponse.message = "Échec de la récupération des données";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

  public async makeImportMedicalSpecialitiesAndActs({ response }: HttpContextContract) {
    const apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };

    try {
      const specialities = await FileReader.readMedicalSpecialities();
      const acts = await FileReader.readMedicalActs();
      const trx = await Database.transaction();

      // Vidage des tables (d'abord les actes puis les spécialités à cause des clés étrangères)
      await this.clearTable('guarantees'); // Actes dans les tables existantes
      await this.clearTable('guarantee_types'); // Spécialités dans les tables existantes
      await this.clearTable('acts'); // Nouvelles tables
      await this.clearTable('specialities'); // Nouvelles tables

      try {
        console.log(`📊 Données à importer: ${specialities.length} spécialités, ${acts.length} actes`);

        // 1. Import dans GuaranteeType (spécialités)
        const guaranteeTypesToInsert = specialities.map(speciality => ({
          name: speciality.name,
          code: speciality.code,
          description: speciality.description,
          isActive: true,
        }));

        const createdGuaranteeTypes = await GuaranteeType.createMany(guaranteeTypesToInsert, { client: trx });
        console.log(`✅ ${createdGuaranteeTypes.length} spécialités importées dans GuaranteeType`);

        // 2. Import dans Guarantee (actes)
        const guaranteesToInsert = acts.map(act => {
          // Trouver le GuaranteeType correspondant
          const guaranteeType = createdGuaranteeTypes.find(gt => gt.code === act.specialityId.toString());

          if (!guaranteeType) return null;

          return {
            guaranteeTypeId: guaranteeType.id,
            insuranceCompanyId: null,
            name: act.name,
            code: act.code,
            description: act.description,
            isActive: true,
            metadata: {
              requires_prescription: act.price ? true : false,
              price: act.price,
              duration: act.duration,
              category: act.category
            }
          };
        }).filter(guarantee => guarantee !== null); // Filtrer les actes sans spécialité

        const createdGuarantees = await Guarantee.createMany(guaranteesToInsert, { client: trx });
        console.log(`✅ ${createdGuarantees.length} actes importés dans Guarantee`);

        // 3. Import dans Speciality (nouvelles tables)
        const specialitiesToInsert = specialities.map(speciality => ({
          name: speciality.name,
          code: speciality.code,
          description: speciality.description,
          isActive: true,
        }));

        const createdSpecialities = await Speciality.createMany(specialitiesToInsert, { client: trx });
        console.log(`✅ ${createdSpecialities.length} spécialités importées dans Speciality`);

        // 4. Import dans Act (nouvelles tables)
        const actsToInsert = acts.map(act => {
          // Trouver la Speciality correspondante
          const speciality = createdSpecialities.find(s => s.code === act.specialityId.toString());

          if (!speciality) return null;

          return {
            specialityId: speciality.id,
            name: act.name,
            code: act.code,
            description: act.description,
            isActive: true,
            metadata: {
              requires_prescription: act.price ? true : false,
              price: act.price,
              duration: act.duration,
              category: act.category
            }
          };
        }).filter(actData => actData !== null); // Filtrer les actes sans spécialité

        const createdActs = await Act.createMany(actsToInsert, { client: trx });
        console.log(`✅ ${createdActs.length} actes importés dans Act`);

        await trx.commit();

        apiResponse.success = true;
        apiResponse.message = "Spécialités médicales et actes importés avec succès";
        apiResponse.result = {
          guaranteeTypes: createdGuaranteeTypes.length,
          guarantees: createdGuarantees.length,
          specialities: createdSpecialities.length,
          acts: createdActs.length,
          totalProcessed: {
            specialities: specialities.length,
            acts: acts.length
          }
        };

        return response.status(201).json(apiResponse);

      } catch (error) {
        await trx.rollback();
        console.error("Erreur lors de l'import:", error);

        apiResponse.message = "Erreur lors de l'import des données";
        apiResponse.except = error.message;
        return response.status(500).json(apiResponse);
      }

    } catch (error) {
      console.error("Erreur:", error);

      apiResponse.message = "Échec de la récupération des données";
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }

}
