import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, HasMany, hasMany, HasOne, hasOne } from '@ioc:Adonis/Lucid/Orm'
import TeamMember from './TeamMember'
import Package from './Package'
import Team from './Team'

export default class TeamGroup extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'public_id' })
  public publicId: string

  @column({ columnName: 'team_id' })
  public teamId: number

  @column({ columnName: 'name' })
  public name: string

  @column({ columnName: 'code' })
  public code: string

  @column({ columnName: 'metadata' })
  public metadata: any | null

  @column({ columnName: 'status' })
  public status: 'active' | 'inactive'

  @column({ columnName: 'description' })
  public description: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Team, {
    localKey: 'id',
    foreignKey: 'teamId',
  })
  public team: BelongsTo<typeof Team>

  @hasMany(() => TeamMember, {
    localKey: 'id',
    foreignKey: 'teamGroupId',
  })
  public members: HasMany<typeof TeamMember>

  @hasOne(() => Package, {
    localKey: 'id',
    foreignKey: 'teamGroupId',
    onQuery(query) {
      query.where('is_custom', true).orderBy('created_at', 'desc');
    },
  })
  public current_package: HasOne<typeof Package>

  @hasMany(() => Package, {
    localKey: 'id',
    foreignKey: 'teamGroupId',
    onQuery(query) {
      query.where('is_custom', false).orderBy('created_at', 'desc');
    },
  })
  public packages: HasMany<typeof Package>
}
