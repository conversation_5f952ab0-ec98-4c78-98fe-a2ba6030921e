import { schema } from '@ioc:Adonis/Core/Validator';
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { ApiResponse } from 'App/Controllers/interfaces';
import Soignant from 'App/Models/Soignant';
import Wallet from 'App/Models/Wallet';
import Database from '@ioc:Adonis/Lucid/Database';
import User from 'App/Models/User';
import Patient from 'App/Models/Patient';
import HealthBook from 'App/Models/HealthBook';

export default class SoignantController {

  public async getSoignants({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 20;
      const sort = request.input('sort') || 'created_at';
      const order = request.input('order') || 'desc';
      const countryId = request.input('country_id', null);
      const cityId = request.input('city_id', null);
      const districtId = request.input('district_id', null);
      const health_instituteId = request.input('health_institute_id', null);
      const status = request.input('status', null) as 'activated' | 'archived' | 'validated' | 'rejected' | 'blocked' | 'pending' | 'kyc';
      const search = request.input('search', null);

      const filters = {
        country_id: countryId,
        city_id: cityId,
        district_id: districtId,
        health_institute_id: health_instituteId,
        status: status
      }

      const query = Soignant.query().orderBy(sort, order).preload('country').preload('city').preload('district');

      if (Object.keys(filters).length > 0) {
        for (const [key, value] of Object.entries(filters)) {
          if (value) {
            query.where(key, value);
          }
        }
      }

      if (search) {
        query.where((query) => {
          query
            .where('first_name', 'like', `%${search}%`)
            .orWhere('last_name', 'like', `%${search}%`)
            .orWhere('phone', 'like', `%${search}%`)
            .orWhere('email', 'like', `%${search}%`)
            .orWhere('address', 'like', `%${search}%`)
            .orWhere('code', 'like', `%${search}%`);
        })
      }

      const soignants = await query.paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Liste des soignants trouvés",
        result: soignants,
      }
    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de récupération des données",
        result: null,
        except: error.message
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async getSoignantDetails({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const soignantId = request.input('soignant_id');
      if (!soignantId) {
        apiResponse = {
          success: false,
          message: "Veuillez fournir un identifiant de soignant",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      const soignant = await Soignant.query().where('id', soignantId).orWhere('code', soignantId)
        .preload('country')
        .preload('city')
        .preload('district')
        .preload('healthInstitutes')
        .limit(1)
        .first();

      if (!soignant) {
        apiResponse = {
          success: false,
          message: "Aucun soignant trouvé",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      const wallet = await Wallet.query().where('user_id', soignant.userId).first();
      apiResponse = {
        success: true,
        message: "Soignant trouvé",
        result: {
          soignant,
          wallet
        }
      }

    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de récupération des données",
        result: null,
        except: error.message
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async updateSoignant({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };
    let status = 201;

    try {
      const payload = await request.validate({
        schema: schema.create({
          soignant_id: schema.number(),
          soignant_code: schema.string(),
          first_name: schema.string.optional(),
          last_name: schema.string.optional(),
          phone: schema.string.optional(),
          email: schema.string.optional(),
          address: schema.string.optional(),
          gender: schema.string.optional(),
          profession: schema.string.optional(),
          birthday_year: schema.number.optional(),
          birthday_month: schema.number.optional(),
          birthday_day: schema.number.optional(),
          domain_id: schema.number.optional(),
          departement: schema.string.optional(),
          is_anit: schema.boolean.optional(),
          zones: schema.array.optional().members(
            schema.object().members({
              city_id: schema.number.optional(),
              district_id: schema.number.optional(),
              address: schema.string.optional(),
            })
          ),
        }),
      });

      const {
        soignant_id,
        soignant_code,
        first_name,
        last_name,
        phone,
        email,
        gender,
        profession,
        address,
        birthday_year,
        birthday_month,
        birthday_day,
        domain_id,
        departement,
        is_anit,
        zones,
      } = payload;

      // Récupérer le soignant correspondant au code fourni
      const soignant = await Soignant.query()
        .where('id', soignant_id)
        .andWhere('code', soignant_code)
        .forUpdate()
        .first();

      if (!soignant) {
        status = 404;
        apiResponse = {
          success: false,
          message: "Aucun soignant trouvé",
          result: null,
        };
        return response.status(status).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        // Vérifier si des informations utilisateur doivent être mises à jour
        let newEmail = email && email !== soignant.email ? false : true;
        let newPhone = phone && phone !== soignant.phone ? false : true;
        if (last_name || first_name || email || phone) {
          const user = await User.query()
            .where('id', soignant.userId)
            .forUpdate()
            .first();

          if (user) {
            let last = last_name ? last_name.toUpperCase() : null;
            let first = first_name ? first_name.toLowerCase() : null;
            let username = last ? `${last}. ${first}` : null;

            let newEmail = email && email !== user.email ? false : true;
            let newPhone = phone && phone !== user.phone ? false : true;

            // Vérifier si le nouvel email est déjà utilisé par un autre utilisateur
            if (newEmail) {
              const existingUser = await User.query()
                .where('email', String(email))
                .andWhere('id', '!=', user.id)
                .first();

              if (existingUser !== null) {
                status = 409;
                apiResponse = {
                  success: false,
                  message: "Cet email est déjà utilisé par un autre utilisateur",
                  result: null,
                };
                return response.status(status).json(apiResponse);
              }
            }

            // Vérifier si le nouveau téléphone est déjà utilisé par un autre utilisateur
            if (newPhone) {
              const existingUser = await User.query()
                .where('phone', String(phone))
                .andWhere('id', '!=', user.id)
                .first();

              if (existingUser) {
                status = 409;
                apiResponse = {
                  success: false,
                  message: "Cet téléphone est déjà utilisé par un autre utilisateur",
                  result: null,
                };
                return response.status(status).json(apiResponse);
              }
            }

            // Appliquer les modifications
            user.merge({
              username: username !== null ? username : user.username,
              email: newEmail ? email : user.email,
              phone: newPhone ? email : user.phone,
            });
            await user.useTransaction(trx).save();
          }
        }

        // Mettre à jour les autres champs du soignant
        await soignant.merge({
          lastName: last_name !== null ? last_name : soignant.lastName,
          firstName: first_name !== null ? first_name : soignant.firstName,
          birthdayYear: birthday_year !== null ? birthday_year : soignant.birthdayYear,
          birthdayMonth: birthday_month !== null ? birthday_month : soignant.birthdayMonth,
          birthdayDay: birthday_day !== null ? birthday_day : soignant.birthdayDay,
          gender: gender !== null ? gender : soignant.gender,
          phone: newPhone ? phone : soignant.phone,
          email: newEmail ? email : soignant.email,
          isAnit: is_anit !== null ? is_anit : soignant.isAnit,
          zones: zones !== null ? JSON.stringify(zones) : soignant.zones,
          profession: profession !== null ? profession : soignant.profession,
          address: address !== null ? address : soignant.address,
          domainId: domain_id !== null ? domain_id : soignant.domainId,
          departement: departement !== null ? departement : soignant.departement,
        }).useTransaction(trx).save();

        // Valider la transaction
        await trx.commit();

        apiResponse = {
          success: true,
          message: "Les données du compte soignant ont été mises à jour avec succès",
          result: soignant,
        };
        return response.status(201).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.log("error", error.message);
        apiResponse = {
          success: false,
          message: "Echec de mise à jour des données du patient",
          result: null,
          except: error.message,
        };
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de mise à jour des données du patient",
        result: null,
        except: error.message,
      };
      return response.status(status).json(apiResponse);
    }
  }

  public async changeStatus({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          soignant_id: schema.number(),
          status: schema.enum(['activated', 'archived', 'validated', 'rejected', 'blocked', 'pending', 'kyc']),
        })
      });

      const { soignant_id, status: status_soignant } = payload;
      const soignant = await Soignant.query().where('id', soignant_id).orWhere('code', soignant_id).forUpdate().first();
      if (!soignant) {
        status = 404;
        apiResponse = {
          success: false,
          message: "Aucun patient trouvé",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      await soignant.merge({
        status: status_soignant
      }).save();

      apiResponse = {
        success: true,
        message: "Statut du patient mis à jour",
        result: soignant
      }
      return response.status(201).json(apiResponse);
    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de mise à jour des données du patient",
        result: null,
        except: error.message
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async validateSoignantKYC({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          soignant_code: schema.string(),
          status: schema.enum(['validated', 'rejected', 'blocked'])
        })
      });
      const { soignant_code, status: status_soignant } = payload;
      const soignant = await Soignant.query().where('id', soignant_code).orWhere('code', soignant_code).forUpdate().first();
      if (!soignant) {
        status = 404;
        apiResponse = {
          success: false,
          message: "Aucun patient trouvé",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      await soignant.merge({
        status: status_soignant
      }).save();
      let message = "";
      if (status_soignant === "validated") {
        message = "Le soignant a été validé avec succès";
      }
      if (status_soignant === "rejected") {
        message = "Le soignant a été rejeté avec succès";
      }
      if (status_soignant === "blocked") {
        message = "Le soignant a été bloqué avec succès";
      }
      //envoie de mail au soignant

      apiResponse = {
        success: true,
        message: message,
        result: soignant
      }
      return response.status(201).json(apiResponse);
    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de mise à jour des données du patient",
        result: null,
        except: error.message
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async getPatientBySoignant({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    };
    let status = 200;

    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 20);
      const soignant_id = request.input('soignant_id');

      // Vérification de la présence de l'identifiant du soignant
      if (!soignant_id) {
        apiResponse = {
          success: false,
          message: "Veuillez fournir un identifiant de soignant",
          result: null,
        };
        return response.status(status).json(apiResponse);
      }

      // Vérification de l'existence du soignant
      const soignant = await Soignant.query().select('id').where('id', soignant_id).first();
      if (!soignant) {
        status = 404;
        apiResponse = {
          success: false,
          message: "Aucun soignant trouvé",
          result: null,
        };
        return response.status(status).json(apiResponse);
      }

      // Récupération des patients créés par le soignant et actifs
      const patientsQuery = Patient.query()
        .where('creator_id', soignant.id)
        .where('creator_type', 'soignant')
        .where('carnet_is_active', true) // Filtrer uniquement les patients actifs
        .preload('bloodGroup')
        .orderBy('created_at', 'desc');

      // Récupération des patients actifs via les carnets de santé
      const healthBooksQuery = HealthBook.query()
        .where('soignant_id', soignant.id)
        .where('status', 1)
        .whereHas('patient', (query) => {
          query.where('carnet_is_active', true); // Filtrer uniquement les patients actifs
        })
        .preload('patient')
        .orderBy('created_at', 'desc');

      // Exécution des requêtes en parallèle
      const [patients_create, health_books] = await Promise.all([
        patientsQuery.paginate(page, limit),
        healthBooksQuery.paginate(page, limit),
      ]);

      // Fusion des patients actifs des deux sources
      const activePatients = [
        ...patients_create.toJSON().data,
        ...health_books.toJSON().data.map((book) => book.patient),
      ];

      // Déduplication des patients (au cas où un patient apparaîtrait dans les deux sources)
      const uniqueActivePatients = activePatients.filter(
        (patient, index, self) => self.findIndex((p) => p.id === patient.id) === index
      );

      // Réponse finale
      apiResponse = {
        success: true,
        message: "Liste des patients actifs",
        result: {
          data: uniqueActivePatients,
          meta: health_books.toJSON().meta, // Utilisation de la pagination des carnets de santé
        },
      };

      return response.status(200).json(apiResponse);
    } catch (error) {
      console.error("Erreur lors de la récupération des patients:", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Échec de la récupération des données des patients",
        result: null,
        except: error.message,
      };
      return response.status(status).json(apiResponse);
    }
  }

}
