import { DateTime } from 'luxon'
import { BaseModel, column, HasMany, hasMany } from '@ioc:Adonis/Lucid/Orm'
import Act from './Act'

export default class Speciality extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public code: string | null

  @column()
  public description: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'is_active' })
  public isActive: boolean = true

  @hasMany(() => Act, {
    foreignKey: 'specialityId',
    localKey: 'id',
  })
  public acts: HasMany<typeof Act>
}
