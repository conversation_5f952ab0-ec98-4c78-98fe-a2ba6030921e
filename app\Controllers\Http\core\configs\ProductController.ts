import { schema } from '@ioc:Adonis/Core/Validator';
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import HelperController from "../../helpers/HelperController";
import { ApiResponse } from 'App/Controllers/interfaces';
import Product from 'App/Models/Product';
import CategoryProduct from 'App/Models/CategoryProduct';
import ProductType from 'App/Models/ProductType';
import Substance from 'App/Models/Substance';


export default class ProductController extends HelperController {

  public async getProducts({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 20;

      const query = Product.query().orderBy('name', 'asc').preload('substances');

      const products = await query.paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Products list",
        result: products,
      }

    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: error.message,
        result: null,
        errors: null
      }
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getCategoriesProducts({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 20;

      const cateogires = await CategoryProduct.query().orderBy('created_at', 'desc').paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Categories list",
        result: cateogires,
      }
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: error.message,
        result: null,
        errors: null
      }
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getProductTypes({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);

      const types = await Substance.query().withCount('products').preload('categories').paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Liste des types de produits trouvées",
        result: types,
      }
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: error.message,
        result: null,
        errors: null
      }
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async createProductCategory({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          name: schema.string(),
          description: schema.string(),
        })
      });

      const { name, description } = payload;
      const category = await CategoryProduct.create({ name: name, description });
      if (!category) {
        apiResponse = {
          success: false,
          message: "Echec de creation de la categorie de produit",
          result: null,
          except: category
        }
        status = 500;
        return response.status(status).json(apiResponse);
      }
      apiResponse = {
        success: true,
        message: "Création de la categorie de produit réussie",
        result: category,
      }
      return response.status(status).json(apiResponse);
    } catch (error) {
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de creation de la categorie de produit",
        result: null,
        except: error.message
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async updateProductCategory({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {} as ApiResponse;
    let status = 200;
    try {
      const payload = request.all();
      const { id } = payload;
      let category = await CategoryProduct.find(id);
      if (!category) {
        apiResponse = {
          success: false,
          message: "Echec de mise à jour de la categorie de produit",
          result: null,
          except: category
        }
        status = 500;
        return response.status(status).json(apiResponse);
      }
      const { name, description } = payload;
      category.name = name ? name : category.name;
      category.description = description ? description : category.description;

      await category.save();
      apiResponse = {
        success: true,
        message: "Mise à jour de la categorie de produit réussie",
        result: category,
      }
      return response.status(status).json(apiResponse);
    } catch (error) {
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de mise à jour de la categorie de produit",
        result: null,
        except: error.message
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async createProductType({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          name: schema.string(),
          description: schema.string(),
          category_product_id: schema.number()
        })
      });
      const { name, description, category_product_id } = payload;
      let slug = name.toLowerCase().replace(/ /g, '-');
      const product_type = await ProductType.create({ name, slug, description, categoryProductId: category_product_id });
      if (!product_type) {
        apiResponse = {
          success: false,
          message: "Echec de création de type de produit",
          result: null,
          except: product_type
        }
        status = 500;
        return response.status(status).json(apiResponse);
      }
      apiResponse = {
        success: true,
        message: "Création de type de produit réussie",
        result: product_type,
      }
      return response.status(status).json(apiResponse);
    } catch (error) {
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de création de type de produit",
        result: null,
        except: error.message
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async updateProductType({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {} as ApiResponse;
    let status = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          id: schema.number(),
          name: schema.string.nullable(),
          description: schema.string.nullable(),
          category_product_id: schema.number.nullable()
        })
      });
      const { id, name, description, category_product_id } = payload;
      let product_type = await ProductType.query().where('id', id).forUpdate().first();
      if (!product_type) {
        apiResponse = {
          success: false,
          message: "Ce type de produit n'existe pas",
          result: null,
          except: product_type
        }
        status = 404;
        return response.status(status).json(apiResponse);
      }
      await product_type.merge({
        name: name ? name : product_type.name,
        description: description ? description : product_type.description,
        categoryProductId: category_product_id ? category_product_id : product_type.categoryProductId
      }).save();

      apiResponse = {
        success: true,
        message: "Mise à jour de type de produit réussie",
        result: product_type,
      }
    } catch (error) {
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de mise à jour de type de produit",
        result: null,
        except: error.message
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async addProduct({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          name: schema.string(),
          eng_name: schema.string.optional(),
          require_diagnostic: schema.boolean(),
          description: schema.string(),
          brand: schema.string.optional(),
          form: schema.string.optional(),
        })
      });
      const { name, eng_name, require_diagnostic, description, brand, form } = payload;
      const checkExist = await Product.query().where('name', name).first();
      if (checkExist) {
        apiResponse = {
          success: false,
          message: "Ce produit existe déjà",
          result: null,
          except: checkExist
        }
        status = 404;
        return response.status(status).json(apiResponse);
      }
      const product = await Product.create({
        name,
        eng_name,
        require_diagnostic,
        description,
        brand,
        form,
      });
      if (!product) {
        apiResponse = {
          success: false,
          message: "Echec de création de produit",
          result: null,
          except: product
        }
        status = 500;
        return response.status(status).json(apiResponse);
      }
      apiResponse = {
        success: true,
        message: "Produit créé avec succès",
        result: product,
      }
      return response.status(status).json(apiResponse);
    } catch (error) {
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de création de produit",
        result: null,
        except: error.message
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async addMultipleProduct({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse;
    let status: number = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          products: schema.array().members(schema.object().members({
            name: schema.string(),
            eng_name: schema.string.optional(),
            type_product_id: schema.number(),
            require_diagnostic: schema.boolean(),
            description: schema.string(),
            brand: schema.string.optional(),
            form: schema.string.optional(),
            code: schema.string.optional(),
          }))
        })
      });
      const { products } = payload;
      let productsToCreate: any[] = [];
      for (const product of products) {
        const existingProduct = await Product.findBy('name', product.name);
        if (!existingProduct) {
          productsToCreate.push({
            name: product.name,
            eng_name: product.eng_name || null,
            productTypeId: product.type_product_id,
            require_diagnostic: product.require_diagnostic,
            description: product.description,
            brand: product.brand || null,
            form: product.form || null,
            code: product.code || null,
          });
        }
      }

      const productsCreated = await Product.createMany(productsToCreate);
      if (!productsCreated) {
        apiResponse = {
          success: false,
          message: "Echec de la création de produit",
          result: null,
        }
        status = 500;
        return response.status(status).json(apiResponse);
      }
      apiResponse = {
        success: true,
        message: "Produit créé avec succès",
        result: productsCreated,
      }
      return response.status(status).json(apiResponse);
    } catch (error) {
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de la création de produit",
        result: null,
        except: error.message
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async updateProduct({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse;
    let status: number = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          product_id: schema.number(),
          name: schema.string.optional(),
          eng_name: schema.string.optional(),
          require_diagnostic: schema.boolean(),
          description: schema.string.optional(),
          brand: schema.string.optional(),
          form: schema.string.optional(),
        })
      });
      const { product_id, name, eng_name, require_diagnostic, description, brand, form } = payload;
      const product = await Product.findOrFail(product_id);
      if (!product) {
        apiResponse = {
          success: false,
          message: "Ce produit n'existe pas",
          result: null,
          except: product
        }
        status = 404;
        return response.status(status).json(apiResponse);
      }
      product.merge({
        name: name !== undefined ? name : product.name,
        eng_name: eng_name !== undefined ? eng_name : product.eng_name,
        require_diagnostic: require_diagnostic !== undefined ? require_diagnostic : product.require_diagnostic,
        description: description !== undefined ? description : product.description,
        brand: brand !== undefined ? brand : product.brand,
        form: form !== undefined ? form : product.form,
      });
      await product.save();

      apiResponse = {
        success: true,
        message: "Mise à jour de produit réussie",
        result: product,
      }
      return response.status(status).json(apiResponse);
    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de mise à jour de produit",
        result: null,
        except: error.message
      }
      return response.status(status).json(apiResponse);
    }
  }



}
