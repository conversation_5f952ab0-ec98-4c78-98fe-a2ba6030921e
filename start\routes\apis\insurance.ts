import Route from '@ioc:Adonis/Core/Route';
import InsuranceCompanyController from 'App/Controllers/Http/core/insurance/InsuranceCompanyController';
import InsuranceAdvisorController from '../../../app/Controllers/Http/core/insurance/InsuranceAdvisorController';

const insuranceCtrl = new InsuranceCompanyController();
const advisorCtrl = new InsuranceAdvisorController();

Route.group(() => {
  Route.group(() => {
    Route.get('/', async (ctx) => {
      return insuranceCtrl.index(ctx);
    });
    Route.post('/create', async (ctx) => {
      return insuranceCtrl.addInsuranceCompany(ctx);
    });
    Route.post('/packages/add', async (ctx) => {
      return insuranceCtrl.addInsurancePackage(ctx);
    });
    Route.post('/beneficiaries/validate', async (ctx) => {
      return insuranceCtrl.validateBeneficiary(ctx);
    });
    Route.post('/beneficiaries/add-subscription', async (ctx) => {
      return insuranceCtrl.addSubscriptionToBeneficiary(ctx);
    });
    Route.get('/details', async (ctx) => {
      return insuranceCtrl.getInsuranceCompanyDetails(ctx);
    });
    Route.get('/subscriptions', async (ctx) => {
      return insuranceCtrl.getInsuranceCompanySubscriptions(ctx);
    });
    Route.get('/fees', async (ctx) => {
      return insuranceCtrl.getInsuranceCompanyFees(ctx);
    });

    Route.group(() => {
      Route.get('/', async (ctx) => {
        return advisorCtrl.index(ctx);
      });
      Route.post('/create', async (ctx) => {
        return advisorCtrl.createInsuranceAdvisor(ctx);
      });
      Route.post('/update', async (ctx) => {
        return advisorCtrl.updateInsuranceAdvisor(ctx);
      });
    }).prefix('advisors');

  }).prefix('insurance-companies');
}).prefix('api').namespace('App/Controllers/Http/core/insurance').middleware('auth:api');
