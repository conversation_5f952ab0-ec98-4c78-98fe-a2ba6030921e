import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, ManyToMany, manyToMany } from '@ioc:Adonis/Lucid/Orm'
import HealthInstitute from './HealthInstitute'
import User from './User'
import Country from './Country'
import City from './City'
import Quarter from './Quarter'

export default class Soignant extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'user_id' })
  public userId: number

  @column({ columnName: 'last_name' })
  public lastName: string

  @column({ columnName: 'first_name' })
  public firstName: string

  @column({ columnName: 'phone' })
  public phone: string

  @column({ columnName: 'email' })
  public email: string

  @column({ columnName: 'address' })
  public address: string | null

  @column({ columnName: 'country_id' })
  public countryId: number

  @column({ columnName: 'city_id' })
  public cityId: number

  @column({ columnName: 'quarter_id' })
  public quarterId: number

  @column({ columnName: 'gender' })
  public gender: string

  @column({ columnName: 'profession' })
  public profession: string | null

  @column({ columnName: 'birthday_year' })
  public birthdayYear: number | null

  @column({ columnName: 'birthday_month' })
  public birthdayMonth: number | null

  @column({ columnName: 'birthday_day' })
  public birthdayDay: number | null

  @column({ columnName: 'domain_id' })
  public domainId: number

  @column({ columnName: 'departement' })
  public departement: string

  @column({ columnName: 'docs' })
  public docs: any

  @column({ columnName: 'health_institute_id' })
  public healthInstituteId: number

  @column({ columnName: 'status' })
  public status: string

  @column({ columnName: 'schedules' })
  public schedules: string

  @column({ columnName: 'code' })
  public code: string

  @column({columnName: 'is_anit'})
  public isAnit: boolean

  @column({ columnName: 'is_advisor' })
  public isAdvisor: boolean

  @column({ columnName: 'zones' })
  public zones: any | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @manyToMany(() => HealthInstitute, {
    pivotTable: 'personals',
    pivotForeignKey: ' health_institute_id',
    pivotRelatedForeignKey: 'soignant_id',
    pivotColumns: [
      'health_institute_id', 'soignant_id','is_active','is_principal','role',
      'departement','matricule','note','affiliation_date','end_date','created_at','updated_at'
    ],
  })
  public healthInstitutes: ManyToMany<typeof HealthInstitute>

  @belongsTo(() => User, {
    localKey: 'userId',
    foreignKey: 'id',
  })
  public user: BelongsTo<typeof User>

  @belongsTo(() => Country, {
    foreignKey: 'countryId',
    localKey: 'id'
  })
  public country: BelongsTo<typeof Country>

  @belongsTo(() => City, {
    foreignKey: 'cityId',
    localKey: 'id'
  })
  public city: BelongsTo<typeof City>

  @belongsTo(() => Quarter, {
    foreignKey: 'quarterId',
    localKey: 'id'
  })
  public district: BelongsTo<typeof Quarter>
}
