import fs from 'node:fs/promises'
import path from 'node:path'
import Application from '@ioc:Adonis/Core/Application'
import { AnalyzeList, ClassificationAct, DCI, PathologyICD, ProductInamList, SymptomICD } from 'App/Controllers/interfaces'

export default class FileReader {
  public static async readJsonFromDatabase<T>(filePath: string): Promise<T> {
    const fullPath = path.join(Application.databasePath(), filePath)
    const rawData = await fs.readFile(fullPath, 'utf-8')
    return JSON.parse(rawData) as T
  }

  public static async readClassificationAct(): Promise<ClassificationAct> {
    return this.readJsonFromDatabase<ClassificationAct>('data/classification_act.json')
  }

  public static async readSymptoms(): Promise<SymptomICD> {
    return this.readJsonFromDatabase<SymptomICD>('data/symptoms.json')
  }

  public static async readPathologies(): Promise<PathologyICD> {
    return this.readJsonFromDatabase<PathologyICD>('data/icd_pathologies.json')
  }

  public static async readInamProducts(): Promise<ProductInamList> {
    return this.readJsonFromDatabase<ProductInamList>('data/inam_products.json')
  }

  public static async readDCI(): Promise<DCI[]> {
    return this.readJsonFromDatabase<DCI[]>('data/dci.json')
  }

  public static async readAnalyzes(): Promise<AnalyzeList> {
    return this.readJsonFromDatabase<AnalyzeList>('data/analyzes.json')
  }
}
