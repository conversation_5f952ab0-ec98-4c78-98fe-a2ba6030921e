import Mail from '@ioc:Adonis/Addons/Mail';
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import { ApiResponse } from "App/Controllers/interfaces";
import Role from "App/Models/Role";
import User from "App/Models/User";
import Wallet from "App/Models/Wallet";

export default class HelperController {

  public async getPatientRoleId() {
    let roleId = 0;
    const res = await Role.query().where('name', 'PATIENT').first();
    if (res) {
      roleId = res.id;
    }
    return roleId;
  }

  public async generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  public async generateCodeParrainage(length: number) {
    const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    let codeParrainage = '';
    for (let i = 0; i < length; i++) {
      codeParrainage += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return codeParrainage.toUpperCase();
  }

  public async generateChannel(username: string) {
    let cleanedUsername = username.replace(/\s+/g, '').toLowerCase();
    let shuffledUsername = this.shuffleString(cleanedUsername);
    let randomPart = Math.random().toString(36).substring(2, 18);
    let timestamp = Date.now().toString();
    let uniqueChannel = `${randomPart}-${timestamp}-${shuffledUsername}`;

    return uniqueChannel;
  }

  // Fonction utilitaire pour mélanger les caractères d'une chaîne
  private shuffleString(input: string): string {
    let array = input.split('');
    for (let i = array.length - 1; i > 0; i--) {
      let j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]]; // Échange des éléments
    }
    return array.join('');
  }

  public async generateWalletCode() {
    const gens = "1234567890";
    let length = 12;
    let code = '';
    for (let i = 0; i < length; i++) {
      code += gens.charAt(Math.floor(Math.random() * gens.length));
    }
    return code;
  }

  public async getWalletByUserId(userId: number): Promise<Wallet | null> {
    const res = Wallet.query().where('user_id', userId).forUpdate().first();
    if (res == null) {
      return null;
    }
    return res;
  }

  public async getBeneficiaryTypeByUserId(userId: number): Promise<string | null> {
    const user = await User.query().where('id', userId).first();
    if (user !== null) {
      if (user.roleId === 3) {
        return 'soignant';
      } else if (user.roleId === 4) {
        return 'pharmacien';
      } else if (user.roleId === 5) {
        return 'laborantin';
      } else if (user.roleId === 6) {
        return 'patient';
      }
    }
    return null;
  }

  public async testSendMail({request,response}: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const email = request.input('email');
      const subject = request.input('subject');
      const content = request.input('message') as string;
      const from = "<EMAIL>";

      const send = await Mail.send((message) => {
        message.from(from);
        message.to(email);
        message.subject(subject);
        message.text(content);
      });

      if (send) {
        apiResponse.success = true;
        apiResponse.message = "Email sent successfully";
      } else {
        apiResponse.message = "Email not sent";
      }
    } catch (error) {
      apiResponse.message = error.message;
    }
    return response.status(status).json(apiResponse);
  }

}
