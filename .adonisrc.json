{"typescript": true, "commands": ["./commands", "@adonisjs/core/build/commands/index.js", "@adonisjs/repl/build/commands", "@adonisjs/lucid/build/commands", "@adonisjs/mail/build/commands"], "exceptionHandlerNamespace": "App/Exceptions/Handler", "aliases": {"App": "app", "Config": "config", "Database": "database", "Contracts": "contracts"}, "preloads": ["./start/routes", "./start/kernel"], "providers": ["./providers/AppProvider", "@adonisjs/core", "@adonisjs/lucid", "@adonisjs/auth", "@adonisjs/mail"], "aceProviders": ["@adonisjs/repl"], "tests": {"suites": [{"name": "functional", "files": ["tests/functional/**/*.spec(.ts|.js)"], "timeout": 60000}]}, "testProviders": ["@japa/preset-adonis/TestsProvider"]}