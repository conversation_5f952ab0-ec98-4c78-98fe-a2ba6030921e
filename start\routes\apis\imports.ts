import Route from '@ioc:Adonis/Core/Route';
import ImportMedicalDataController from 'App/Controllers/Http/core/imports/ImportMedicalDataController';

const controller = new ImportMedicalDataController();

Route.group(() => {
  Route.group(() => {
    Route.post('/product-categories', async (ctx) => {
      return controller.makeImportProductCategory(ctx);
    });
    Route.post('/symptoms', async (ctx) => {
      return controller.makeImportSymptoms(ctx);
    });
    Route.post('/substances', async (ctx) => {
      return controller.makeImportDCI(ctx);
    });
    Route.post('/pathologies', async (ctx) => {
      return controller.makeImportPathologies(ctx);
    });
    Route.post('/products', async (ctx) => {
      return controller.makeImportProducts(ctx);
    });
    Route.post('/analyzes', async (ctx) => {
      return controller.makeImportAnalyzes(ctx);
    });
    Route.post('/medical-specialities-and-acts', async (ctx) => {
      return controller.makeImportMedicalSpecialitiesAndActs(ctx);
    });
  }).prefix('imports');
}).prefix('api').namespace('App/Controllers/Http/core/imports').middleware('auth:api');
