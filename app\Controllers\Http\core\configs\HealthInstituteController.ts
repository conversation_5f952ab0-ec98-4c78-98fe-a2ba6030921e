import { schema } from '@ioc:Adonis/Core/Validator';
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { ApiResponse, UserStatus } from 'App/Controllers/interfaces';
import HealthInstitute from 'App/Models/HealthInstitute';
import TypeHealthInstitute from 'App/Models/TypeHealthInstitute';
import HospitalManager from 'App/Models/HospitalManager';
import Personal from 'App/Models/Personal';
import User from 'App/Models/User';
import { DateTime } from 'luxon';
import HelperController from '../../helpers/HelperController';
import Database from '@ioc:Adonis/Lucid/Database';
import Wallet from 'App/Models/Wallet';

export default class HealthInstituteController extends HelperController {

  public async getHealthInstitutes({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 20;
      const type_health_institute_id = request.input('type_health_institute_id', null);
      const country_id = request.input('country_id', null);
      const city_id = request.input('city_id', null);
      const district_id = request.input('district_id', null);

      const query = HealthInstitute.query().orderBy('created_at', 'desc').preload('city').preload('district').preload('country').preload('typeHealthInstitute');

      if (type_health_institute_id) {
        query.where('type_health_institute_id', type_health_institute_id);
      }
      if (country_id) {
        query.where('country_id', country_id);
      }
      if (city_id) {
        query.where('city_id', city_id);
      }
      if (district_id) {
        query.where('district_id', district_id);
      }
      const healthInstitutes = await query.paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Liste des instituts de santé trouvées",
        result: healthInstitutes,
      }
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: error.message,
        result: null,
        errors: null
      }
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async getHealthInstituteTypes({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 20;
      const healthInstitutes = await TypeHealthInstitute.query().orderBy('created_at', 'desc').paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Liste des types d'instituts de santé trouvées",
        result: healthInstitutes,
      }
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: error.message,
        result: null,
        errors: null
      }
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async addHealthInstituteType({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse;
    let status: number = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          name: schema.string(),
          description: schema.string(),
        })
      });
      const { name, description } = payload;
      const checkExist = await TypeHealthInstitute.query().where('name', name).first();
      if (checkExist) {
        apiResponse = {
          success: false,
          message: "Ce type d'institut de santé existe déjà",
          result: null,
          except: checkExist
        }
        status = 400;
        return response.status(status).json(apiResponse);
      }

      const healthInstituteType = await TypeHealthInstitute.create({
        name,
        description,
      });
      if (!healthInstituteType) {
        apiResponse = {
          success: false,
          message: "Echec de création de type d'institut de santé",
          result: null,
          except: healthInstituteType
        }
        status = 500;
        return response.status(status).json(apiResponse);
      }
      apiResponse = {
        success: true,
        message: "Création de type d'institut de santé réussie",
        result: healthInstituteType,
      }
      return response.status(status).json(apiResponse);
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: "Echec de création de type d'institut de santé",
        result: null,
        except: error.message
      }
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async updateHealthInstituteType({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse;
    let status: number = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          type_health_institute_id: schema.number(),
          name: schema.string(),
          description: schema.string(),
        })
      });
      const { type_health_institute_id, name, description } = payload;
      const healthInstituteType = await TypeHealthInstitute.query().where('id', type_health_institute_id).forUpdate().first();
      if (!healthInstituteType) {
        apiResponse = {
          success: false,
          message: "Ce type d'institut de santé n'existe pas",
          result: null,
          except: healthInstituteType
        }
        status = 404;
        return response.status(status).json(apiResponse);
      }

      await healthInstituteType.merge({
        name: name || healthInstituteType.name,
        description: description || healthInstituteType.description,
      }).save();

      apiResponse = {
        success: true,
        message: "Mise à jour de type d'institut de santé réussie",
        result: healthInstituteType,
      }
      return response.status(status).json(apiResponse);
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: "Echec de mise à jour de type d'institut de santé",
        result: null,
        except: error.message
      }
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }


  public async addHealthInstitute({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    };
    let status: number = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          name: schema.string(),
          type_health_institute_id: schema.number(),
          phone: schema.string(),
          email: schema.string.optional(),
          website: schema.string.optional(),
          country_id: schema.number(),
          city_id: schema.number(),
          quarter_id: schema.number.optional(),
          prefecture: schema.string.optional(),
          address: schema.string.optional(),
          description: schema.string.optional(),
          opening_hours: schema.object().anyMembers(),
          is_partner: schema.boolean(),
          responsable: schema.object().members({
            first_name: schema.string(),
            last_name: schema.string(),
            phone: schema.string(),
            email: schema.string(),
            country_id: schema.number.nullable(),
            city_id: schema.number.nullable(),
            gender: schema.enum(['M', 'F'] as const),
            birthday_year: schema.number.nullable(),
            birthday_month: schema.number.nullable(),
            birthday_day: schema.number.nullable(),
            profession: schema.string(),
            role: schema.string()
          }),
          status: schema.string.optional()
        })
      });
      const { name, type_health_institute_id, phone, email, website, country_id, city_id, quarter_id, prefecture, address, description, is_partner, responsable, opening_hours } = payload;
      const checkExist = await HealthInstitute.query().where('name', name).first();
      if (checkExist) {
        apiResponse = {
          success: false,
          message: "Ce institut de santé existe déjà",
          result: null,
          except: checkExist
        }
        status = 400;
        return response.status(status).json(apiResponse);
      }

      const trx = await Database.transaction();
      try {
        const healthInstitute = await HealthInstitute.create({
          name,
          typeHealthInstituteId: type_health_institute_id,
          phone,
          email,
          website,
          countryId: country_id,
          cityId: city_id,
          quarterId: quarter_id,
          prefecture,
          address,
          description,
          openingHours: JSON.stringify(opening_hours),
          isPartner: payload.is_partner || false,
        },{client: trx});
        if (!healthInstitute) {
          apiResponse = {
            success: false,
            message: "Echec de création de l'institut de santé",
            result: null,
            except: healthInstitute
          }
          status = 500;
          return response.status(status).json(apiResponse);
        }

        if(is_partner && responsable){
          const checkUser = await User.query().where('email', responsable.email).orWhere('phone', responsable.phone).first();
          if (checkUser) {
            await trx.rollback();
            apiResponse.message = "Ce responsable existe déjà";
            status = 400;
            return response.status(status).json(apiResponse);
          }
          const codeP = await this.generateCodeParrainage(8);
          const codeParrainage = {
            create_account: 0,
            active_qrcode: 0,
            adhesion_fees: 0,
            plan: 1,
            activeMoney: false,
          };
          let username = responsable.first_name + responsable.last_name.toUpperCase();
          const user = await User.create({
            username: username,
            phone: responsable.phone,
            email: responsable.email,
            countryId: country_id,
            status: UserStatus.Actived,
            roleId: 15,
            languageId: 1,
            activatedAt: DateTime.now(),
            codeParrainage: codeP,
            parrainage: JSON.stringify(codeParrainage),
            isOld: false,
            password: responsable.phone,
          }, { client: trx });
          if (!user) {
            await trx.rollback();
            apiResponse.message = "Echec de création de l'utilisateur";
            status = 500;
            return response.status(status).json(apiResponse);
          }
          //create health_institute_manager
          const manager = await HospitalManager.create({
            healthInstituteId: healthInstitute.id,
            userId: user.id,
            firstName: responsable.first_name,
            lastName: responsable.last_name,
            phone: responsable.phone,
            email: responsable.email,
            countryId: country_id,
            gender: responsable.gender,
            birthdayYear: responsable.birthday_year !== null ? responsable.birthday_year : undefined,
            birthdayMonth: responsable.birthday_month,
            birthdayDay: responsable.birthday_day,
            profession: responsable.profession,
            role: responsable.role,
          }, { client: trx });
          if (!manager) {
            await trx.rollback();
            apiResponse.message = "Echec de création du responsable";
            apiResponse.except = manager;
            status = 500;
            return response.status(status).json(apiResponse);
          }

          const walletCode = await this.generateWalletCode();
          const healthInstituteWallet = await Wallet.create({
            ownerType: 'health_institute',
            ownerId: healthInstitute.id,
            libelle: "WALLET " + healthInstitute.name,
            typeWalletId: 2,
            code: walletCode,
          }, { client: trx });

          if (!healthInstituteWallet) {
            await trx.rollback();
            apiResponse.message = "Echec de création du wallet";
            apiResponse.except = healthInstituteWallet;
            status = 500;
            return response.status(status).json(apiResponse);
          }

          let channel = await this.generateChannel(healthInstitute.name);
          if (!channel) {
            await trx.rollback();
            apiResponse.message = "Echec de création du channel";
            apiResponse.except = channel;
            status = 500;
            return response.status(status).json(apiResponse);
          }
          await healthInstitute.merge({
            channel: channel,
          }).useTransaction(trx).save();

          apiResponse = {
            success: true,
            message: "Création de l'institut de santé réussie",
            result: {
              healthInstitute,
              responsable: manager,
              wallet: healthInstituteWallet,
              password: responsable.phone as string
            },
          }
        }

        await trx.commit();
        apiResponse = {
          success: true,
          message: "Création de l'institut de santé réussie",
          result: healthInstitute,
        }
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.log("error", error);
        apiResponse = {
          success: false,
          message: "Echec de création de l'institut de santé",
          result: null,
          except: error.message
        }
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: "Echec de création de l'institut de santé",
        result: null,
        except: error.message,
        errors: error.messages.errors
      }
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async configHealthInstitute({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    }
    let status: number = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          health_institute_id: schema.number(),
          is_partner: schema.boolean(),
          responsable: schema.object().members({
            first_name: schema.string(),
            last_name: schema.string(),
            phone: schema.string(),
            email: schema.string(),
            country_id: schema.number.nullable(),
            city_id: schema.number.nullable(),
            gender: schema.enum(['M', 'F'] as const),
            birthday_year: schema.number.nullable(),
            birthday_month: schema.number.nullable(),
            birthday_day: schema.number.nullable(),
            profession: schema.string(),
            role: schema.string()
          })
        })
      });
      const { health_institute_id, is_partner, responsable } = payload;
      const healthInstitute = await HealthInstitute.query().where('id', health_institute_id).forUpdate().first();
      if (!healthInstitute) {
        apiResponse = {
          success: false,
          message: "Ce institut de santé n'existe pas",
          result: null,
        }
        status = 404;
        return response.status(status).json(apiResponse);
      }
      const trx = await Database.transaction();
      try {
        await healthInstitute.merge({
          isPartner: is_partner,
        }).useTransaction(trx).save();
        if(is_partner && responsable){
          const checkUser = await User.query().where('email', responsable.email).orWhere('phone', responsable.phone).first();
          if (checkUser) {
            await trx.rollback();
            apiResponse.message = "Ce responsable existe déjà";
            status = 400;
            return response.status(status).json(apiResponse);
          }
          const codeP = await this.generateCodeParrainage(8);
          const codeParrainage = {
            create_account: 0,
            active_qrcode: 0,
            adhesion_fees: 0,
            plan: 1,
            activeMoney: false,
          };
          let username = responsable.first_name + responsable.last_name.toUpperCase();
          const user = await User.create({
            username: username,
            phone: responsable.phone,
            email: responsable.email,
            countryId: healthInstitute.countryId,
            status: UserStatus.Actived,
            roleId: 15,
            languageId: 1,
            activatedAt: DateTime.now(),
            codeParrainage: codeP,
            parrainage: JSON.stringify(codeParrainage),
            isOld: false,
            password: responsable.phone,
          }, { client: trx });
          if (!user) {
            await trx.rollback();
            apiResponse.message = "Echec de création de l'utilisateur";
            status = 500;
            return response.status(status).json(apiResponse);
          }
          //create health_institute_manager
          const manager = await HospitalManager.create({
            healthInstituteId: healthInstitute.id,
            userId: user.id,
            firstName: responsable.first_name,
            lastName: responsable.last_name,
            phone: responsable.phone,
            email: responsable.email,
            countryId: healthInstitute.countryId,
            gender: responsable.gender,
            birthdayYear: responsable.birthday_year!== null? responsable.birthday_year : undefined,
            birthdayMonth: responsable.birthday_month,
            birthdayDay: responsable.birthday_day,
            profession: responsable.profession,
            role: responsable.role,
          },{ client: trx });
          if (!manager) {
            await trx.rollback();
            apiResponse.message = "Echec de création du responsable";
            apiResponse.except = manager;
            status = 500;
            return response.status(status).json(apiResponse);
          }
          const walletCode = await this.generateWalletCode();
          const healthInstituteWallet = await Wallet.create({
            ownerType: 'health_institute',
            ownerId: healthInstitute.id,
            libelle: "WALLET " + healthInstitute.name,
            typeWalletId: 2,
            code: walletCode,
          }, { client: trx });
          if (!healthInstituteWallet) {
            await trx.rollback();
            apiResponse.message = "Echec de création du wallet";
            apiResponse.except = healthInstituteWallet;
            status = 500;
            return response.status(status).json(apiResponse);
          }

          apiResponse = {
            success: true,
            message: "Configuration de l'institut de santé réussie",
            result: {
              healthInstitute,
              responsable: manager,
              wallet: healthInstituteWallet,
            },
          }
        }
        await trx.commit();
        apiResponse = {
          success: true,
          message: "Configuration de l'institut de santé réussie",
          result: healthInstitute,
        }
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.log("error", error);
        apiResponse = {
          success: false,
          message: "Echec de configuration de l'institut de santé",
          result: null,
          except: error.message
        }
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: "Echec de configuration de l'institut de santé",
        result: null,
        except: error.message,
        errors: error?.messages
      }
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async updateHealthInstitute({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse;
    let status: number = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          health_institute_id: schema.number(),
          name: schema.string(),
          type_health_institute_id: schema.number(),
          phone: schema.string(),
          email: schema.string(),
          website: schema.string(),
          country_id: schema.number(),
          city_id: schema.number(),
          quarter_id: schema.number(),
          prefecture: schema.string(),
          address: schema.string(),
          description: schema.string(),
        })
      });
      const { health_institute_id, name, type_health_institute_id, phone, email, website, country_id, city_id, quarter_id, prefecture, address, description } = payload;
      const healthInstitute = await HealthInstitute.query().where('id', health_institute_id).forUpdate().first();
      if (!healthInstitute) {
        apiResponse = {
          success: false,
          message: "Cet institut de santé n'existe pas",
          result: null,
          except: healthInstitute
        }
        status = 404;
        return response.status(status).json(apiResponse);
      }

      await healthInstitute.merge({
        name: name || healthInstitute.name,
        typeHealthInstituteId: type_health_institute_id || healthInstitute.typeHealthInstituteId,
        phone: phone || healthInstitute.phone,
        email: email || healthInstitute.email,
        website: website || healthInstitute.website,
        countryId: country_id || healthInstitute.countryId,
        cityId: city_id || healthInstitute.cityId,
        quarterId: quarter_id || healthInstitute.quarterId,
        prefecture: prefecture || healthInstitute.prefecture,
        address: address || healthInstitute.address,
        description: description || healthInstitute.description,
      }).save();

      apiResponse = {
        success: true,
        message: "Mise à jour de l'institut de santé réussie",
        result: healthInstitute,
      }
      return response.status(status).json(apiResponse);
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: "Echec de mise à jour de l'institut de santé",
        result: null,
        except: error.message
      }
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async getHealthInstituteManagers({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 20;
      const health_institute_id = request.input('health_institute_id', null);
      const query = HospitalManager.query().orderBy('created_at', 'desc').preload('healthInstitute');

      if (health_institute_id) {
        query.where('health_institute_id', health_institute_id);
      }
      const managers = await query.paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Liste des managers d'instituts de santé trouvées",
        result: managers,
      }
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: error.message,
        result: null,
        errors: null
      }
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

  public async addHealthInstituteManager({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse;
    let status: number = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          last_name: schema.string(),
          first_name: schema.string(),
          phone: schema.string(),
          email: schema.string(),
          address: schema.string(),
          country_id: schema.number(),
          city_id: schema.number(),
          quarter_id: schema.number(),
          gender: schema.string(),
          birthday_year: schema.number(),
          birthday_month: schema.number(),
          birthday_day: schema.number(),
          profession: schema.string(),
          health_institute_id: schema.number(),
        })
      });
      const { last_name, first_name, phone, email, address, country_id, city_id, quarter_id, gender, birthday_year, birthday_month, birthday_day, profession, health_institute_id } = payload;
      const checkExist = await HospitalManager.query().where('last_name', last_name).where('first_name', first_name).first();
      if (checkExist) {
        apiResponse = {
          success: false,
          message: "Ce manager existe déjà",
          result: null,
          except: checkExist
        }
        status = 400;
        return response.status(status).json(apiResponse);
      }

      let codeP = await this.generateCodeParrainage(8);

      const codeParrainage = {
        create_account: 0,
        active_qrcode: 0,
        adhesion_fees: 0,
        plan: 1,
        activeMoney: false
      }
      const trx = await Database.transaction();
      try {
        const user = await User.create({
          username: first_name,
          email: email,
          phone: phone,
          countryId: country_id,
          status: UserStatus.Actived,
          roleId: 15,
          languageId: 1,
          activatedAt: DateTime.now(),
          codeParrainage: codeP,
          parrainage: JSON.stringify(codeParrainage),
          isOld: false,
        });

        if (!user) {
          await trx.rollback();
          apiResponse = {
            success: false,
            message: "Echec de création de l'utilisateur",
            result: null,
            except: user
          }
          status = 500;
          return response.status(status).json(apiResponse);
        }

        const healthInstituteManager = await HospitalManager.create({
          userId: user.id,
          lastName: last_name,
          firstName: first_name,
          phone,
          email,
          address,
          countryId: country_id,
          cityId: city_id,
          quarterId: quarter_id,
          gender,
          birthdayYear: birthday_year,
          birthdayMonth: birthday_month,
          birthdayDay: birthday_day,
          profession,
          healthInstituteId: health_institute_id,
        });
        if (!healthInstituteManager) {
          await trx.rollback();
          apiResponse = {
            success: false,
            message: "Echec de création de manager",
            result: null,
            except: healthInstituteManager
          }
          status = 500;
          return response.status(status).json(apiResponse);
        }
        await trx.commit();
        apiResponse = {
          success: true,
          message: "Création de manager réussie",
          result: healthInstituteManager,
        }
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.log("error", error.message);
        apiResponse = {
          success: false,
          message: "Echec de création de l'utilisateur",
          result: null,
          except: error.message
        }
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: "Echec de création de manager",
        result: null,
        except: error.message
      }
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async updateHealthInstituteManager({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse;
    let status: number = 200;
    try {
      const payload = await request.validate({
        schema: schema.create({
          health_institute_manager_id: schema.number(),
          last_name: schema.string(),
          first_name: schema.string(),
          phone: schema.string(),
          email: schema.string(),
          address: schema.string(),
          country_id: schema.number(),
          city_id: schema.number(),
          quarter_id: schema.number(),
          gender: schema.string(),
          birthday_year: schema.number(),
          birthday_month: schema.number(),
          birthday_day: schema.number(),
          profession: schema.string(),
          health_institute_id: schema.number(),
        })
      });
      const { health_institute_manager_id, last_name, first_name, phone, email, address, country_id, city_id, quarter_id, gender, birthday_year, birthday_month, birthday_day, profession, health_institute_id } = payload;
      const healthInstituteManager = await HospitalManager.query().where('id', health_institute_manager_id).forUpdate().first();
      if (!healthInstituteManager) {
        apiResponse = {
          success: false,
          message: "Ce manager n'existe pas",
          result: null,
          except: healthInstituteManager
        }
        status = 404;
        return response.status(status).json(apiResponse);
      }

      await healthInstituteManager.merge({
        firstName: first_name || healthInstituteManager.firstName,
        lastName: last_name || healthInstituteManager.lastName,
        phone: phone || healthInstituteManager.phone,
        email: email || healthInstituteManager.email,
        address: address || healthInstituteManager.address,
        countryId: country_id || healthInstituteManager.countryId,
        cityId: city_id || healthInstituteManager.cityId,
        quarterId: quarter_id || healthInstituteManager.quarterId,
        gender: gender || healthInstituteManager.gender,
        birthdayYear: birthday_year || healthInstituteManager.birthdayYear,
        birthdayMonth: birthday_month || healthInstituteManager.birthdayMonth,
        birthdayDay: birthday_day || healthInstituteManager.birthdayDay,
        profession: profession || healthInstituteManager.profession,
        healthInstituteId: health_institute_id || healthInstituteManager.healthInstituteId,
      }).save();

      apiResponse = {
        success: true,
        message: "Mise à jour de manager réussie",
        result: healthInstituteManager,
      }
      return response.status(status).json(apiResponse);
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: "Echec de mise à jour de manager",
        result: null,
        except: error.message
      }
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async getHealthInstitutePersonals({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    }
    let status = 200;
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;
      const health_institute_id = request.input('health_institute_id', null);
      const query = Personal.query().orderBy('created_at', 'desc').preload('healthInstitute').preload('soignant');

      if (health_institute_id) {
        query.where('health_institute_id', health_institute_id);
      }
      const personals = await query.paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Liste des personals d'instituts de santé trouvées",
        result: personals,
      }
    } catch (error) {
      console.log("error", error.message);
      apiResponse = {
        success: false,
        message: error.message,
        result: null,
        errors: null
      }
      status = 500;
    }
    return response.status(status).json(apiResponse);
  }

}
