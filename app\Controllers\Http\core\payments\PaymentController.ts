import { schema } from '@ioc:Adonis/Core/Validator';

import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import HelperController from '../../helpers/HelperController';
import { ApiResponse, PaymentResponse } from 'App/Controllers/interfaces';
import PaymentIntent from 'App/Models/PaymentIntent';
import PaymentApiTrait from './traits/PaymentApiTrait';
import { Payment } from 'App/Models/Payment';
import { DateTime } from 'luxon';
import Database from '@ioc:Adonis/Lucid/Database';
import WalletTrait from './traits/WalletTrait';

export default class PaymentController extends HelperController {

  public async getPaymentIntents({ request, response }: HttpContextContract) {
    let status = 200;
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
    }
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const sort = request.input('order', 'desc');
      const order_by = request.input('order_by', 'created_at');
      const payment_type_id = request.input('payment_type_id', null);
      const payment_status = request.input('status', null) as 'pending' | 'success' | 'failed' | 'cancelled' | 'paid ' | 'expired' | null;

      const filters = {
        payment_type_id: payment_type_id,
        status: payment_status
      }
      const query = PaymentIntent.query().orderBy(order_by, sort)
        .preload('paymentType')
        .preload('user')
        .preload('beneficiary');

      if (Object.keys(filters).length > 0) {
        for (const [key, value] of Object.entries(filters)) {
          if (value) {
            query.where(key, value);
          }
        }
      }

      const paymentIntents = await query.paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Récupération des intentions de paiement effectuée",
        result: paymentIntents,
      }
    } catch (error) {
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de la récupération des intentions de paiement",
        result: null,
        except: error.message
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async getPaymentIntentDetails({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const paymentIntentId = request.input('payment_intent_id', null);
      if (!paymentIntentId) {
        status = 400;
        apiResponse = {
          success: false,
          message: "Le paramètre payment_intent_id est manquant",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      const paymentIntent = await PaymentIntent.query().where('id', paymentIntentId)
        .preload('paymentType')
        .preload('user')
        .preload('beneficiary')
        .preload('payment')
        .first();
      if (!paymentIntent) {
        status = 404;
        apiResponse = {
          success: false,
          message: "L'intention de paiement n'existe pas",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      let ptrait = new PaymentApiTrait();
      const paymentData = await ptrait.getPaymentStatus(paymentIntent.reference.toString());
      if (paymentData.success) {
        apiResponse = {
          success: true,
          message: "Récupération des données de l'intention de paiement effectuée",
          result: {
            intent: paymentIntent,
            payment: paymentData.result
          }
        }
      }
    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de récupération des données de l'intention de paiement",
        result: null,
        except: error.message
      }
    }
    return response.status(status).json(apiResponse);
  }




  public async fixPaymentIntentV2({ request, response }: HttpContextContract) {

    try {
      const payload = await request.validate({
        schema: schema.create({
          reference: schema.number(),
        })
      });
      const { reference } = payload;
      const paymentIntent = await PaymentIntent.query().where('reference', reference).first();
      if (!paymentIntent) {
        return response.status(404).json({
          success: false,
          message: "Aucun intention de paiement trouvé",
          result: null
        });
      }

      if (paymentIntent.status === 'paid' && paymentIntent.paidAt) {
        const payment = await Payment.query().where('payment_intent_id', Number(paymentIntent.id)).first();
        const wallet = await this.getWalletByUserId(paymentIntent.userId);
        return response.status(200).json({
          success: true,
          message: "Cette facture a été déjà réglée avec succès",
          result: {
            intent: paymentIntent,
            payment,
            wallet,
          }
        });
      }

      const trait = new PaymentApiTrait();
      const checkPayment = await trait.getPaymentStatus(reference.toString());

      if (!checkPayment.success) {
        return response.status(500).json({
          success: false,
          message: "Echec de récupération des données de paiement",
          result: null,
          except: checkPayment.except
        });
      }

      const paymentResult = checkPayment.result as PaymentResponse;

      if (paymentResult.state !== 'Paid') {
        const message = paymentResult.state === 'Error'
          ? "Echec, votre paiement n'a pas abouti avec succès, veuillez réessayer"
          : "Votre paiement est en attente de validation";
        return response.status(400).json({
          success: false,
          message,
          result: null,
          except: paymentResult
        });
      }

      // Retry logic for database operations
      const maxRetries = 3;
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        const trx = await Database.transaction();

        try {
          // Re-check the payment intent status to avoid race conditions
          const currentIntent = await PaymentIntent.query()
            .where('id', paymentIntent.id)
            .useTransaction(trx)
            .forUpdate() // Lock the row for update
            .first();

          if (!currentIntent) {
            await trx.rollback();
            return response.status(404).json({
              success: false,
              message: "Intention de paiement introuvable",
              result: null
            });
          }

          if (currentIntent.status === 'paid') {
            await trx.rollback();
            return response.status(409).json({
              success: false,
              message: "Cette intention de paiement a déjà été traitée",
              result: null
            });
          }

          // Update intent and create payment
          // Ensure metadata is an object (handled by model hooks)
          const intentMetadata = currentIntent.metadata || {};

          const newMetadata = {
            ...intentMetadata,
            state: paymentResult.state,
            bill_url: paymentResult.bill_url,
          };

          currentIntent.metadata = newMetadata;
          currentIntent.status = 'paid';
          currentIntent.paidAt = DateTime.now();
          await currentIntent.useTransaction(trx).save();

          const metadata = {
            state: paymentResult.state,
            bill_url: paymentResult.bill_url,
            billed_amount: paymentResult.billed_amount,
            merchant_reference: paymentResult.merchant_reference,
            order_reference: paymentResult.order_reference,
            date_created: paymentResult.date_create,
            payment_method: intentMetadata.payments_method || intentMetadata.payment_method || null,
          };

          const trxRef = await this.generateUUID();
          const beneficiary_type = await this.getBeneficiaryTypeByUserId(currentIntent.userId);

          const resPayment = await Payment.create({
            amount: currentIntent.amountPaid,
            beneficiaryId: currentIntent.beneficiaryId,
            beneficiaryType: String(beneficiary_type) as "patient" | "laborantin" | "pharmacien" | "soignant" | null,
            paymentTypeId: currentIntent.paymentTypeId,
            metadata,
            status: currentIntent.status,
            paidAt: currentIntent.paidAt,
            paymentGatewayId: intentMetadata.gateway_id ?? null,
            paymentIntentId: currentIntent.id,
            trxRef,
            description: `Le portefeuille a été rechargé avec un montant de ${currentIntent.amountPaid} ${intentMetadata.currency || currentIntent.currency || 'XOF'}`,
          }, { client: trx });

          if (!resPayment) {
            await trx.rollback();
            return response.status(500).json({
              success: false,
              message: "Une erreur est survenue lors de la création du paiement, veuillez réessayer",
              result: null
            });
          }

          const wallet = await this.getWalletByUserId(currentIntent.userId);
          if (!wallet) {
            await trx.rollback();
            return response.status(404).json({
              success: false,
              message: "Portefeuille introuvable",
              result: null
            });
          }

          const wtrait = new WalletTrait();
          const credite = await wtrait.crediteWallet(wallet, currentIntent.amountPaid, "deposit", resPayment.id, trx);
          if (!credite.success) {
            await trx.rollback();
            return response.status(500).json({
              success: false,
              message: "Une erreur est survenue lors du crédit du portefeuille, veuillez réessayer",
              result: null
            });
          }

          await trx.commit();

          return response.status(200).json({
            success: true,
            message: "Le paiement a été effectué avec succès",
            result: {
              intent: currentIntent,
              payment: resPayment,
              wallet,
            }
          });

        } catch (error) {
          await trx.rollback();

          // Handle lock timeout with retry
          if (error.code === 'ER_LOCK_WAIT_TIMEOUT' && attempt < maxRetries) {
            console.log(`Lock timeout on attempt ${attempt}, retrying in ${attempt * 1000}ms...`);
            await new Promise(resolve => setTimeout(resolve, attempt * 1000));
            continue; // Retry the loop
          }

          // For other errors, throw to be caught by outer catch
          throw error;
        }
      }

      // If we reach here, all retries failed
      return response.status(503).json({
        success: false,
        message: "Le système est temporairement occupé, veuillez réessayer dans quelques instants",
        result: null
      });

    } catch (error) {
      console.log("error in fix payment intent v2", error);

      // Handle custom error objects first
      if (error && typeof error === 'object' && 'status' in error && 'success' in error) {
        return response.status(error.status).json({
          success: error.success,
          message: error.message,
          result: error.result
        });
      }

      // Handle specific database error types
      let message = "Echec du traitement de la requête";
      let status = 500;

      if (error.code === 'ER_LOCK_WAIT_TIMEOUT') {
        message = "Le système est temporairement occupé, veuillez réessayer dans quelques instants";
        status = 503; // Service Unavailable
      } else if (error.message && error.message.includes("déjà été traitée")) {
        message = error.message;
        status = 409; // Conflict
      } else if (error.message && error.message.includes("Portefeuille introuvable")) {
        message = error.message;
        status = 404; // Not Found
      }

      return response.status(status).json({
        success: false,
        message: message,
        result: null,
        except: error.message
      });
    }
  }

  public async getPayments({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const sort = request.input('order', 'desc');
      const order_by = request.input('order_by', 'created_at');

      const payments = await Payment.query().orderBy(order_by, sort)
        .preload('paymentType')
        .preload('patient')
        .paginate(page, limit);
      apiResponse = {
        success: true,
        message: "Récupération des données de paiement effectuée",
        result: payments,
      }
    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de récupération des données",
        result: null,
        except: error.message
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async getPaymentDetails({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const paymentId = request.input('payment_id');
      if (!paymentId) {
        status = 400;
        apiResponse = {
          success: false,
          message: "Le paramètre payment_id est manquant",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      const payment = await Payment.query().where('id', paymentId)
        .preload('paymentIntent')
        .preload('paymentType')
        .preload('patient')
        .first();
      if (!payment) {
        status = 404;
        apiResponse = {
          success: false,
          message: "Aucun paiement trouvé",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      apiResponse = {
        success: true,
        message: "Récupération des données de paiement effectuée",
        result: payment
      }
    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de récupération des données",
        result: null,
        except: error.message
      }
    }
    return response.status(status).json(apiResponse);
  }
}
