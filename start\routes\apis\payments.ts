import Route from '@ioc:Adonis/Core/Route';
import PaymentController from 'App/Controllers/Http/core/payments/PaymentController';
import PaymentSettingController from 'App/Controllers/Http/core/payments/PaymentSettingController';
const paymentCtrl = new PaymentController();
const settingCtrl = new PaymentSettingController();

Route.group(() => {
  Route.group(() => {
    Route.get('/', async (ctx) => {
      return paymentCtrl.getPayments(ctx);
    });
    Route.get('/details', async (ctx) => {
      return paymentCtrl.getPaymentDetails(ctx);
    });
    Route.get('/intents', async (ctx) => {
      return paymentCtrl.getPaymentIntents(ctx);
    });
    Route.get('/intents/details', async (ctx) => {
      return paymentCtrl.getPaymentIntentDetails(ctx);
    });
    Route.post('/intents/fix', async (ctx) => {
      return paymentCtrl.fixPaymentIntentV2(ctx);
    });
  }).prefix('payments');
  Route.group(() => {
    Route.get('/gateways', async (ctx) => {
      return settingCtrl.getPaymentGateways(ctx);
    });
    Route.get('/gateways/details', async (ctx) => {
      return settingCtrl.getPaymentGatewayDetail(ctx);
    });
    Route.post('/gateways/update', async (ctx) => {
      return settingCtrl.updateGateway(ctx);
    });
    Route.get('/types', async (ctx) => {
      return settingCtrl.getPaymentTypes(ctx);
    });
    Route.post('/types/update', async (ctx) => {
      return settingCtrl.updatePaymentType(ctx);
    });
  }).prefix('payment-settings');
}).prefix('api').namespace('App/Controllers/Http/core/payments').middleware('auth:api');
