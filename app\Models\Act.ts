import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Speciality from './Speciality'

export default class Act extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'speciality_id' })
  public specialityId: number

  @column()
  public name: string

  @column()
  public code: string | null

  @column()
  public description: string | null

  @column({ columnName: 'is_active' })
  public isActive: boolean = true

  @column({ columnName: 'metadata' })
  public metadata: {
    requires_prescription?: boolean,
    price?: number,
    duration?: string,
    category?: string
  } | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column.dateTime()
  public deletedAt: DateTime | null

  @belongsTo(() => Speciality, {
    foreignKey: 'specialityId',
    localKey: 'id',
  })
  public speciality: BelongsTo<typeof Speciality>
}
