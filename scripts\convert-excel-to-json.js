const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

/**
 * Script pour convertir le fichier Excel des spécialités médicales et actes en JSON
 */

function convertExcelToJson() {
  console.log('🔄 Début de la conversion Excel vers JSON...');

  try {
    // Chemin vers le fichier Excel
    const excelFilePath = path.join(__dirname, '../database/data/Spécialités médicales & actes.xls');

    // Vérifier si le fichier existe
    if (!fs.existsSync(excelFilePath)) {
      throw new Error(`Fichier Excel non trouvé: ${excelFilePath}`);
    }

    console.log(`📖 Lecture du fichier: ${excelFilePath}`);

    // Lire le fichier Excel
    const workbook = XLSX.readFile(excelFilePath);

    console.log(`📋 Feuilles trouvées: ${workbook.SheetNames.join(', ')}`);

    // Structure de données finale
    const medicalData = {
      specialities: [],
      acts: [],
      metadata: {
        convertedAt: new Date().toISOString(),
        totalSpecialities: 0,
        totalActs: 0
      }
    };

    // Traiter chaque feuille
    workbook.SheetNames.forEach((sheetName, index) => {
      console.log(`\n🔍 Traitement de la feuille: "${sheetName}" (index: ${index})`);

      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1, // Utiliser les indices de ligne comme clés
        defval: '' // Valeur par défaut pour les cellules vides
      });

      console.log(`   📊 Nombre de lignes: ${jsonData.length}`);

      if (sheetName === 'Feuil1') {
        // Première feuille = Spécialités
        processSpecialities(jsonData, medicalData);
      } else if (!isNaN(parseInt(sheetName))) {
        // Feuilles numérotées = Actes pour chaque spécialité
        const specialityNumber = parseInt(sheetName);
        processActs(jsonData, medicalData, specialityNumber, sheetName);
      }
    });

    // Mettre à jour les métadonnées
    medicalData.metadata.totalSpecialities = medicalData.specialities.length;
    medicalData.metadata.totalActs = medicalData.acts.length;

    // Sauvegarder les données
    saveJsonFiles(medicalData);

    console.log('\n✅ Conversion terminée avec succès!');
    console.log(`📈 Statistiques:`);
    console.log(`   - Spécialités: ${medicalData.metadata.totalSpecialities}`);
    console.log(`   - Actes: ${medicalData.metadata.totalActs}`);

  } catch (error) {
    console.error('❌ Erreur lors de la conversion:', error.message);
    process.exit(1);
  }
}

function processSpecialities(data, medicalData) {
  console.log('   🏥 Traitement des spécialités médicales...');

  for (let i = 0; i < data.length; i++) {
    const row = data[i];

    // Ignorer les lignes vides
    if (!row || row.length === 0 || !row[0]) continue;

    const speciality = {
      id: parseInt(row[0]), // ID = numéro de la spécialité
      code: String(row[0]).trim(), // Le numéro de la spécialité
      name: row[1] ? String(row[1]).trim() : `Spécialité ${row[0]}`, // Le nom de la spécialité
      description: null, // Pas de description dans cette structure
      sheetIndex: parseInt(row[0]), // Index correspondant aux feuilles d'actes
      createdAt: new Date().toISOString()
    };

    if (speciality.name && speciality.id) {
      medicalData.specialities.push(speciality);
      console.log(`     ✓ Spécialité ajoutée: ${speciality.name} (ID: ${speciality.id})`);
    }
  }
}

function processActs(data, medicalData, specialityNumber, sheetName) {
  console.log(`   🩺 Traitement des actes pour la spécialité ${specialityNumber}: "${sheetName}"`);

  // Trouver la spécialité correspondante
  const speciality = medicalData.specialities.find(s => s.id === specialityNumber);

  if (!speciality) {
    console.log(`     ⚠️  Aucune spécialité trouvée pour le numéro ${specialityNumber}`);
    return;
  }

  // Ignorer la première ligne si c'est un en-tête
  const startRow = data[0] && typeof data[0][0] === 'string' &&
    (data[0][0].toLowerCase().includes('acte') || data[0][0].toLowerCase().includes('nom')) ? 1 : 0;

  for (let i = startRow; i < data.length; i++) {
    const row = data[i];

    // Ignorer les lignes vides
    if (!row || row.length === 0 || !row[0]) continue;

    const act = {
      id: medicalData.acts.length + 1,
      specialityId: speciality.id,
      specialityName: speciality.name,
      code: String(row[0]).trim(), // Code de l'acte
      name: row[1] ? String(row[1]).trim() : String(row[0]).trim(), // Description de l'acte
      description: row[1] ? String(row[1]).trim() : null,
      price: row[2] ? parseFloat(String(row[2]).replace(/[^\d.,]/g, '').replace(',', '.')) || null : null,
      duration: row[3] ? String(row[3]).trim() : null,
      category: row[4] ? String(row[4]).trim() : null,
      createdAt: new Date().toISOString()
    };

    if (act.name) {
      medicalData.acts.push(act);
      console.log(`     ✓ Acte ajouté: ${act.name}`);
    }
  }
}

function saveJsonFiles(medicalData) {
  console.log('\n💾 Sauvegarde des fichiers JSON...');

  const outputDir = path.join(__dirname, '../database/data');

  // Sauvegarder le fichier complet
  const fullDataPath = path.join(outputDir, 'medical-specialities-and-acts.json');
  fs.writeFileSync(fullDataPath, JSON.stringify(medicalData, null, 2), 'utf8');
  console.log(`   ✓ Fichier complet sauvegardé: ${fullDataPath}`);

  // Sauvegarder les spécialités séparément
  const specialitiesPath = path.join(outputDir, 'medical-specialities.json');
  fs.writeFileSync(specialitiesPath, JSON.stringify(medicalData.specialities, null, 2), 'utf8');
  console.log(`   ✓ Spécialités sauvegardées: ${specialitiesPath}`);

  // Sauvegarder les actes séparément
  const actsPath = path.join(outputDir, 'medical-acts.json');
  fs.writeFileSync(actsPath, JSON.stringify(medicalData.acts, null, 2), 'utf8');
  console.log(`   ✓ Actes sauvegardés: ${actsPath}`);

  // Sauvegarder un fichier de mapping spécialité -> actes
  const mappingData = medicalData.specialities.map(speciality => ({
    ...speciality,
    acts: medicalData.acts.filter(act => act.specialityId === speciality.id)
  }));

  const mappingPath = path.join(outputDir, 'specialities-with-acts.json');
  fs.writeFileSync(mappingPath, JSON.stringify(mappingData, null, 2), 'utf8');
  console.log(`   ✓ Mapping sauvegardé: ${mappingPath}`);
}

// Exécuter le script
if (require.main === module) {
  convertExcelToJson();
}

module.exports = { convertExcelToJson };
