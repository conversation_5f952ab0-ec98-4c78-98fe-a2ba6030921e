const axios = require('axios');

/**
 * Script de test pour l'importation des spécialités et actes médicaux
 */

async function testImport() {
  console.log('🧪 Test de l\'importation des spécialités et actes médicaux...');
  console.log('📋 Import dans 4 tables:');
  console.log('   1. guarantee_types (spécialités - tables existantes)');
  console.log('   2. guarantees (actes - tables existantes)');
  console.log('   3. specialities (spécialités - nouvelles tables)');
  console.log('   4. acts (actes - nouvelles tables)');
  console.log('');

  try {
    // Configuration de la requête
    const baseURL = 'http://localhost:3333'; // Ajustez selon votre configuration
    const endpoint = '/api/imports/medical-specialities-and-acts';

    console.log(`📡 Envoi de la requête POST vers: ${baseURL}${endpoint}`);

    // Note: Vous devrez ajouter un token d'authentification valide
    const response = await axios.post(`${baseURL}${endpoint}`, {}, {
      headers: {
        'Content-Type': 'application/json',
        // 'Authorization': 'Bearer YOUR_TOKEN_HERE' // Décommentez et ajoutez votre token
      },
      timeout: 120000 // 2 minutes de timeout pour l'import
    });

    console.log('✅ Importation réussie !');
    console.log('Status:', response.status);
    console.log('📊 Résultats:');

    if (response.data && response.data.result) {
      const result = response.data.result;
      console.log(`   • GuaranteeTypes: ${result.guaranteeTypes || 0} spécialités`);
      console.log(`   • Guarantees: ${result.guarantees || 0} actes`);
      console.log(`   • Specialities: ${result.specialities || 0} spécialités`);
      console.log(`   • Acts: ${result.acts || 0} actes`);
      console.log(`   • Total traité: ${result.totalProcessed?.specialities || 0} spécialités, ${result.totalProcessed?.acts || 0} actes`);
    }

    console.log('\n📝 Réponse complète:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('❌ Erreur lors du test:');

    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.error('Pas de réponse reçue:', error.message);
    } else {
      console.error('Erreur de configuration:', error.message);
    }
  }
}

// Exécuter le test
if (require.main === module) {
  testImport();
}

module.exports = { testImport };
