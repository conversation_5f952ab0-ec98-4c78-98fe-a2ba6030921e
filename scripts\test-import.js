const axios = require('axios');

/**
 * Script de test pour l'importation des spécialités et actes médicaux
 */

async function testImport() {
  console.log('🧪 Test de l\'importation des spécialités et actes médicaux...');
  
  try {
    // Configuration de la requête
    const baseURL = 'http://localhost:3333'; // Ajustez selon votre configuration
    const endpoint = '/api/imports/medical-specialities-and-acts';
    
    console.log(`📡 Envoi de la requête POST vers: ${baseURL}${endpoint}`);
    
    // Note: Vous devrez ajouter un token d'authentification valide
    const response = await axios.post(`${baseURL}${endpoint}`, {}, {
      headers: {
        'Content-Type': 'application/json',
        // 'Authorization': 'Bearer YOUR_TOKEN_HERE' // Décommentez et ajoutez votre token
      },
      timeout: 60000 // 60 secondes de timeout
    });
    
    console.log('✅ Réponse reçue:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ Erreur lors du test:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else if (error.request) {
      console.error('Pas de réponse reçue:', error.message);
    } else {
      console.error('Erreur de configuration:', error.message);
    }
  }
}

// Exécuter le test
if (require.main === module) {
  testImport();
}

module.exports = { testImport };
