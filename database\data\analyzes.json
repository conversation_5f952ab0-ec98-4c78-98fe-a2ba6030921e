{"categories": [{"name": "Ana<PERSON><PERSON>", "types": [{"name": "Examens Sanguins", "subTypes": [{"name": "<PERSON><PERSON>", "analyses": [{"code": "B100", "name": "Acide urique sanguin"}, {"code": "B101", "name": "Albuminémie (Méthode immunologique)"}, {"code": "B102", "name": "Ammoniémie"}, {"code": "B103", "name": "Bilirubine (Totale Directe et Indirecte)"}, {"code": "B104", "name": "Calcium"}, {"code": "B105", "name": "Chlore"}, {"code": "B106", "name": "Cholestérol total"}, {"code": "B107", "name": "Cholestérol <PERSON>"}, {"code": "B108", "name": "Cholestérol HDL"}, {"code": "B109", "name": "Cholesterol LDL"}, {"code": "B110", "name": "Cholestérol HDL+LDL"}, {"code": "B111", "name": "Créatinine"}, {"code": "B112", "name": "Electrophorèse de l'Hémoglobine"}, {"code": "B113", "name": "Electrophorèse des lipides"}, {"code": "B114", "name": "Electrophorèse des protides"}, {"code": "B115", "name": "<PERSON><PERSON><PERSON>"}, {"code": "B116", "name": "Fer sérique + CTF"}, {"code": "B117", "name": "Fructosamine"}, {"code": "B118", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": "B119", "name": "Hémoglobine glycosylée"}, {"code": "B120", "name": "Ionogramme complet (Na, K, CI, Prot, RA)"}, {"code": "B121", "name": "Lipid<PERSON>"}, {"code": "B122", "name": "Lipoprotéine A"}, {"code": "B123", "name": "Lipoprotéine B"}, {"code": "B124", "name": "Lipoprotéines A+B"}, {"code": "B125", "name": "Lithium"}, {"code": "B126", "name": "Magnésium Plasmatique"}, {"code": "B127", "name": "Magnésium Erythrocytaire"}, {"code": "B128", "name": "Oxalates"}, {"code": "B129", "name": "<PERSON>os<PERSON><PERSON>"}, {"code": "B130", "name": "Protéines"}, {"code": "B131", "name": "Potassium"}, {"code": "B132", "name": "Réserve Alcaline"}, {"code": "B133", "name": "Sodium"}, {"code": "B134", "name": "Triglycérides"}, {"code": "B135", "name": "Urée"}, {"code": "B442", "name": "<PERSON><PERSON><PERSON><PERSON> sang (Si Prescription isolée)"}, {"code": "B443", "name": "Osmolarité 'urine' (Si Prescription isolée)"}, {"code": "B444", "name": "Alpha 2 macroglobuline - par immunomarquage"}, {"code": "B445", "name": "Acide lactique"}, {"code": "B446", "name": "Débit de Filtration Glomérulaire - DFG"}, {"code": "B447", "name": "Cryoglobulines recherche"}, {"code": "B448", "name": "Cryoglobulines recherche + Typage"}, {"code": "B449", "name": "Gazométrie (PH, pCO2, pO2, SatO2)"}]}, {"name": "Enzymologie", "analyses": [{"code": "B136", "name": "Aldolase"}, {"code": "B137", "name": "<PERSON><PERSON>"}, {"code": "B138", "name": "Creatinine phosphokinase CPK"}, {"code": "B139", "name": "Creatinine phosphokinase CPK(MB)"}, {"code": "B140", "name": "G6 PDH"}, {"code": "B141", "name": "Gamma glutamyl transférase (G G T)"}, {"code": "B142", "name": "Lacticodeshydrogenase LDH"}, {"code": "B143", "name": "Phosphatases Alcalines"}, {"code": "B144", "name": "Phosphatases acides"}, {"code": "B145", "name": "Phosphatases prostatiques"}, {"code": "B146", "name": "Transaminases 0 (TGO)"}, {"code": "B147", "name": "Transaminases P (TGP)"}, {"code": "B148", "name": "Lipase"}, {"code": "B149", "name": "Troponine"}]}, {"name": "Hormonologie", "analyses": [{"code": "B150", "name": "B H CG quantitatif"}, {"code": "B151", "name": "B H CG qualitatif"}, {"code": "B152", "name": "17 B Oestradiol"}, {"code": "B153", "name": "Cortisol"}, {"code": "B154", "name": "<PERSON><PERSON><PERSON>"}, {"code": "B155", "name": "Insuline"}, {"code": "B156", "name": "L H"}, {"code": "B157", "name": "Progestérone"}, {"code": "B158", "name": "Prolactine"}, {"code": "B159", "name": "Triodothyronine: T3 ou FT3"}, {"code": "B160", "name": "Thyroxine: T4 ou FT4"}, {"code": "B161", "name": "Thyroxine libre: T4 libre"}, {"code": "B162", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": "B163", "name": "TSH"}, {"code": "B164", "name": "T S H ulta sensible"}, {"code": "B165", "name": "Triodothyronine libre: T3L"}, {"code": "B458", "name": "F S H - Technique Immunoenzymatique"}, {"code": "B459", "name": "Inhibine B - Technique Immunoenzymatique"}, {"code": "B460", "name": "AMH: hormone anti-mullérienne - Technique Immunoenzymatique"}, {"code": "B461", "name": "Triple Test (Dépistage Trisomie 21) - 3 marqueurs: AFP, bHCG, Oestriol"}, {"code": "B462", "name": "Testostérone LIBRE - Technique Immunoenzymatique"}, {"code": "B463", "name": "Insuline LIBRE - Technique Immunoenzymatique"}, {"code": "B464", "name": "PTH Intacte - Technique Immunoenzymatique"}, {"code": "B465", "name": "Testostérone urinaire - par Immunomarquage"}, {"code": "B466", "name": "Cortisol libre urinaire - par Immunomarquage"}]}, {"name": "Cardiologie", "analyses": [{"code": "B450", "name": "Myoglobine - Technique Immunoenzymatique"}, {"code": "B451", "name": "Enzyme de conversion de l'angiotensine"}, {"code": "B452", "name": "Beta2microglobuline - par immunomarquage"}, {"code": "B453", "name": "Homocystéine - par immunomarquage ou HPLC"}, {"code": "B454", "name": "BNP - par immunomarquage"}, {"code": "B455", "name": "NT ProBNP - par immunomarquage"}, {"code": "B456", "name": "Procalcitonine - par immunomarquage"}, {"code": "B457", "name": "CRP ultrasensible - par immunomarquage"}]}, {"name": "Maladies Héréditaires du Métabolisme", "analyses": [{"code": "B467", "name": "Chromatographie qualitative des acides aminés (Sang, urine, LCR)"}, {"code": "B468", "name": "Dosage et identification des MPS (GAG) urinaires"}, {"code": "B469", "name": "CCM sucres ou oligosaccharides urinaires"}, {"code": "B470", "name": "Dosage fluorimétrique de la Phénylalanine (suivi PCU)"}, {"code": "B471", "name": "Dosage d'un métabolite: Galactose-1 Phosphate, Carnitine, Acide pipécolique"}, {"code": "B472", "name": "Diagnostic de la Galactosémie congénitale"}, {"code": "B473", "name": "Recherche d'un déficit enzymatique sur leucocytes (1 activité) Cotation limitée à 5 activités"}, {"code": "B474", "name": "Chromatographie quantitative des acides aminés"}, {"code": "B475", "name": "Diagnostic enzymatique de la maladie de Hurler"}, {"code": "B476", "name": "Etude des acyl carnitines"}, {"code": "B477", "name": "Recherche d'une mutation responsable de la maladie"}, {"code": "B478", "name": "Acides gras à très longue chaîne (CPG)"}, {"code": "B479", "name": "Identification d'une hémoglobinose (Isofocalisation + HPLC des chaînes)"}, {"code": "B480", "name": "Diagnostic anténatal d'une hémoglobinopathie"}]}, {"name": "Folates", "analyses": [{"code": "B485", "name": "Folates plasmatiques (vitamine B9) - par Immunomarquage"}, {"code": "B486", "name": "Folates érythrocytaires - par Immunomarquage"}]}]}, {"name": "Examens Urinaires", "subTypes": [{"name": "<PERSON><PERSON>", "analyses": [{"code": "B166", "name": "Acétone (recherche)"}, {"code": "B167", "name": "Acide <PERSON>"}, {"code": "B168", "name": "Calcium"}, {"code": "B169", "name": "Chlore"}, {"code": "B170", "name": "Créatinine"}, {"code": "B171", "name": "Electrophorèse des Protéines urinaires"}, {"code": "B172", "name": "Identification d'un calcul urinaire"}, {"code": "B173", "name": "PH"}, {"code": "B174", "name": "<PERSON>os<PERSON><PERSON>"}, {"code": "B175", "name": "Potassium"}, {"code": "B176", "name": "<PERSON><PERSON><PERSON> (recherche)"}, {"code": "B177", "name": "Pro<PERSON>ine (dosage)"}, {"code": "B178", "name": "Sédiment minéral"}, {"code": "B179", "name": "Sodium"}, {"code": "B180", "name": "Sucre (recherche)"}, {"code": "B181", "name": "Sucre (Recherche + dosage)"}, {"code": "B182", "name": "Urée"}, {"code": "B481", "name": "Ion<PERSON><PERSON> urinaire (Na-K-Cl-Ca)"}, {"code": "B482", "name": "Microalbuminurie de 24H - par immunoNéphélémetrie"}, {"code": "B483", "name": "Microalbuminurie exprimée en gramme/gramme de créatinine"}]}, {"name": "Hormonologie", "analyses": [{"code": "B183", "name": "Acide 5 Hydroxyindol acétique (5HIA)"}, {"code": "B184", "name": "<PERSON><PERSON><PERSON> (VMA)"}, {"code": "B185", "name": "<PERSON><PERSON><PERSON>"}, {"code": "B186", "name": "Catécholamines"}, {"code": "B187", "name": "17 Cétostéroides"}, {"code": "B188", "name": "Dérivés méthoxylés: (Métanéphrines Normétanéphrines)"}, {"code": "B189", "name": "Phenolsteroides"}, {"code": "B190", "name": "Porphyrines (recherche)"}, {"code": "B191", "name": "Porphyrines (dosage)"}, {"code": "B192", "name": "Prégnandiol"}, {"code": "B193", "name": "Prolans B"}, {"code": "B194", "name": "Test de grossesse"}]}]}]}, {"name": "Analyses Hématologiques", "types": [{"name": "Examens Hématologiques", "subTypes": [{"name": "Cytologie", "analyses": [{"code": "B212", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": "B213", "name": "<PERSON><PERSON><PERSON>"}, {"code": "B214", "name": "Hématocrite"}, {"code": "B215", "name": "Hémoglobine"}, {"code": "B216", "name": "Numération Formule (Globules rouges/Blancs/plaquettes)"}, {"code": "B217", "name": "Numération des Plaquettes"}, {"code": "B218", "name": "Myélogramme: Etude cytologique"}, {"code": "B219", "name": "Myélogramme: Etude cytochimique"}, {"code": "B220", "name": "Recherche d'hématies ponctuées"}, {"code": "B221", "name": "Résistance globulaire"}, {"code": "B222", "name": "Réticulocytes"}, {"code": "B223", "name": "Vitesse de sédimentation"}, {"code": "B495", "name": "Etude de la Moelle Osseuse: Examen cytologique et cytochimique"}]}, {"name": "ImmunoHématologie", "analyses": [{"code": "B224", "name": "Coombs Direct/Coombs indirect"}, {"code": "B225", "name": "Recherche d'agglutinines irrégulières: RAI"}, {"code": "B226", "name": "Recherche et dosage d'agglutinines irrégulières"}, {"code": "B227", "name": "Cross match: <PERSON>e poche"}, {"code": "B228", "name": "Cross match: chaque poche supplémentaire"}, {"code": "B229", "name": "Cross match: Groupe ABO et Rhésus"}, {"code": "B230", "name": "Phénotype"}, {"code": "B484", "name": "Etude des Hémoglobines - HPLC"}, {"code": "B491", "name": "Identification des agglutinines irrégulières"}, {"code": "B492", "name": "Phénotype Rhésus CcEe + Kell"}]}, {"name": "Hémostase", "analyses": [{"code": "B231", "name": "Dosage des Facteurs de la coagulation autre que: VIII- IX-XIII"}, {"code": "B232", "name": "Dosage des facteurs VIII-IX ou XIII"}, {"code": "B233", "name": "Fibrinogène"}, {"code": "B234", "name": "Héparinémie"}, {"code": "B235", "name": "PDF"}, {"code": "B236", "name": "<PERSON><PERSON> de prothrombine"}, {"code": "B237", "name": "Temps de saignements (TS)"}, {"code": "B238", "name": "TS + Temps de coagulation"}, {"code": "B239", "name": "Temps de céphaline kaolin (TCK)"}, {"code": "B240", "name": "<PERSON><PERSON> de Howell"}, {"code": "B487", "name": "Antithrombine III - Technique chromogénique ou Néphélémétrie"}, {"code": "B488", "name": "Protéine C - Technique chromogénique ou EIA"}, {"code": "B489", "name": "Protéine S - Technique chromogénique ou Turbidimétrie"}, {"code": "B490", "name": "Activité Anti-Xa - T chronométrique ou chromogénique"}, {"code": "B493", "name": "Temps de saignement IVY (Méthode du Brassard)"}, {"code": "B494", "name": "D Dimères - turbidimétrie ou Immunomarquage"}]}]}]}, {"name": "Bactériologie Parasitologie Mycologie", "types": [{"name": "Examens Bactériologiques Parasitologiques Mycologiques", "analyses": [{"code": "B241", "name": "Cytologie. Culture. Identification"}, {"code": "B242", "name": "Antibiogramme"}, {"code": "B243", "name": "Sulfamidogramme en cas de prescription isolée"}, {"code": "B244", "name": "Cytologie"}, {"code": "B245", "name": "Examen parasitologique urinaire ou vaginal"}, {"code": "B246", "name": "Examen mycologique: Recherche"}, {"code": "B247", "name": "Examen mycologique: (culture, identification)"}, {"code": "B248", "name": "Recherche de BK + Concentration"}, {"code": "B249", "name": "Culture sur milieu de Lowenstein"}, {"code": "B250", "name": "Culture (Aéorobie-Anaérobie)"}, {"code": "B251", "name": "Hémoculture: Identification"}, {"code": "B252", "name": "Recherche Chlamydiae direct par lA ou IF"}, {"code": "B253", "name": "Recherche Mycoplasmes par culture"}, {"code": "B496", "name": "Hélicobacter pylori - Antigènes"}, {"code": "B497", "name": "Charge virale de l'Hépatite C"}, {"code": "B498", "name": "Génotypage du virus de L'Hépatite C"}, {"code": "B499", "name": "Fibrotest-Actitest"}, {"code": "B500", "name": "Anti Fongigramme"}, {"code": "B501", "name": "BK Antibiogramme"}, {"code": "B502", "name": "Chlamydiae trachomatis PCR"}, {"code": "B503", "name": "Hémoculture Aerobie + Anaerobie - (T.fluorométrique sur Automates)"}, {"code": "B504", "name": "BK - Mycobactéries PCR"}, {"code": "B505", "name": "Hydatidose Confirmation par Western Blot"}, {"code": "B506", "name": "Toxoplasmose - IgA"}, {"code": "B507", "name": "Toxoplasmose Avidité"}, {"code": "B508", "name": "Recherche d'ADN par PCR"}, {"code": "B509", "name": "Leishmaniose - Confirmation par western-blot"}, {"code": "B510", "name": "Recherche d'antigènes aspergillaires circulants"}, {"code": "B511", "name": "Recherche d'antigènes candidosiques circulants"}, {"code": "B512", "name": "Recherche et titrage de l'antigène cryptococcique dans le sang ou le LCR"}]}]}, {"name": "Sero-Diagnostic", "types": [{"name": "Sérologie Bactérienne", "analyses": [{"code": "B263", "name": "Antistaphylolysine"}, {"code": "B264", "name": "Antistreptodornase"}, {"code": "B265", "name": "Antistreptokinase"}, {"code": "B266", "name": "Antistreptolysine (recherche, titrage)"}, {"code": "B267", "name": "Antistreptohyaluronidase"}, {"code": "B268", "name": "<PERSON><PERSON><PERSON> (Wright)"}, {"code": "B269", "name": "Chlamyd<PERSON> trachomatis"}, {"code": "B270", "name": "Hélicobacter pylori"}, {"code": "B271", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": "B272", "name": "Gonococcie"}, {"code": "B273", "name": "Léptospirose"}, {"code": "B274", "name": "Listériose"}, {"code": "B275", "name": "Maladie de Lyme"}, {"code": "B276", "name": "Mycoplasmes génitaux (hominis et uréalyticum)"}, {"code": "B277", "name": "Mycoplasma pneumoniae"}, {"code": "B278", "name": "Ricketlsiose"}, {"code": "B279", "name": "<PERSON><PERSON><PERSON> (<PERSON><PERSON> et Félix)"}, {"code": "B280", "name": "ShigeIlose: l'espèce"}, {"code": "B281", "name": "Streptozyme"}, {"code": "B282", "name": "Sérologie du BK"}, {"code": "B283", "name": "Sérologie de la Syphilis: VDRL qualitatif"}, {"code": "B284", "name": "Sérologie de la Syphilis: VDRL quantitatif"}, {"code": "B285", "name": "Sérologie de la Syphilis: TPHA qualitatif"}, {"code": "B286", "name": "Sérologie de la Syphilis: TPHA quantitatif"}, {"code": "B287", "name": "Sérologie de la Syphilis: <PERSON>"}, {"code": "B288", "name": "Sérologie de la Syphilis: FT A Absorbens IgG"}, {"code": "B289", "name": "Sérologie de la Syphilis: IgM"}]}, {"name": "Sérologie <PERSON>", "analyses": [{"code": "B290", "name": "Ankylostomiase"}, {"code": "B291", "name": "Amibiase"}, {"code": "B292", "name": "Aspergillose"}, {"code": "B293", "name": "Bilharziose"}, {"code": "B294", "name": "Hydatidose (par hémaglutination)"}, {"code": "B295", "name": "Blastomycose"}, {"code": "B296", "name": "Candidose"}, {"code": "B297", "name": "Coccidiomycose"}, {"code": "B298", "name": "Cryptococcose"}, {"code": "B299", "name": "Criptosporidiose"}, {"code": "B300", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": "B301", "name": "Distomatose"}, {"code": "B302", "name": "Filariose"}, {"code": "B303", "name": "Histoplasmose"}, {"code": "B304", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": "B305", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": "B306", "name": "Toxocarose"}, {"code": "B307", "name": "Toxoplasmose IgG"}, {"code": "B308", "name": "Toxoplasmose IgM"}, {"code": "B309", "name": "Taenia"}, {"code": "B310", "name": "Trichinose"}, {"code": "B311", "name": "Trichomonas"}, {"code": "B312", "name": "Trypanosome"}]}, {"name": "Sérologie <PERSON>", "analyses": [{"code": "B313", "name": "Adénovirus"}, {"code": "B314", "name": "Grippe antigène-B"}, {"code": "B315", "name": "Hépatite A: IgG"}, {"code": "B316", "name": "Hépatite A: IgM"}, {"code": "B317", "name": "Hépatite B: AgHbs"}, {"code": "B318", "name": "Hépatite B: Ac anti Hbs"}, {"code": "B319", "name": "Hépatite B: Ag Hbe"}, {"code": "B320", "name": "Hépatite B: Ac anti Hbe"}, {"code": "B321", "name": "Hépatite B: Ac anti Hbc"}, {"code": "B322", "name": "Hépatite B: Ac anti Hbc IgM"}, {"code": "B323", "name": "Hépatite B: ADN par PCR"}, {"code": "B324", "name": "Hépatite C: <PERSON><PERSON><PERSON><PERSON>"}, {"code": "B325", "name": "Hépatite C: test de confirmation"}, {"code": "B326", "name": "Hépatite C: ARN par PCR"}, {"code": "B327", "name": "Herpes virus simplex type I (lgG ou IgM)"}, {"code": "B328", "name": "Herpes virus simplex type Il (lgG ou IgM)"}, {"code": "B329", "name": "HIV 1+ HIV(1+2) Dépistage"}, {"code": "B330", "name": "<PERSON><PERSON><PERSON> de <PERSON>"}, {"code": "B331", "name": "Charge virale HIV"}, {"code": "B332", "name": "Mononucléose infectieuse: MNI Test"}, {"code": "B333", "name": "<PERSON> et B<PERSON><PERSON><PERSON>"}, {"code": "B334", "name": "Ac EBNA"}, {"code": "B335", "name": "Ac VCA(lgG+lgM)"}, {"code": "B336", "name": "AcEA"}, {"code": "B337", "name": "Oreillons(lgG+lgM)"}, {"code": "B338", "name": "Para influenzae(paramyxovirus)"}, {"code": "B339", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": "B340", "name": "Poliomyélite"}, {"code": "B341", "name": "Rotavirus"}, {"code": "B342", "name": "Rougeole IgG ou IgM"}, {"code": "B343", "name": "Rubéole IgG"}, {"code": "B344", "name": "Rubéole IgM"}, {"code": "B345", "name": "Varicelle et Zona"}, {"code": "B346", "name": "Virus Syncitial respiratoire(VRS)"}]}, {"name": "Immunologie", "subTypes": [{"name": "Histocompatibilité", "analyses": [{"code": "B513", "name": "HLA B27 - Technique Microlymphocytotoxicité (LCT)"}, {"code": "B514", "name": "HLA B5 - Technique Microlymphocytotoxicité (LCT)"}, {"code": "B515", "name": "Typage HLA classe I - Technique Microlymphocytotoxicité (LCT)"}, {"code": "B516", "name": "Typage HLA classe II par biologie moléculaire"}, {"code": "B517", "name": "Cross match HLA - Technique microlymphocytotoxicité"}, {"code": "B518", "name": "Typage HLA locus A par technique luminex"}, {"code": "B519", "name": "Typage HLA locus B par technique luminex"}, {"code": "B520", "name": "Typage HLA locus DR par technique luminex"}, {"code": "B521", "name": "Typage HLA locus DQ par technique luminex"}, {"code": "B522", "name": "Recherche d'anticorps anti HLA par technique ELISA"}, {"code": "B523", "name": "Identification d'anticorps anti HLA classe I par ELISA"}, {"code": "B524", "name": "Identification d'anticorps anti HLA classe II par ELISA"}]}, {"name": "Autoimmunité", "analyses": [{"code": "B347", "name": "Auto anticorps anti nucléaires"}, {"code": "B348", "name": "Anticorps anti thyroidiens: AC anti Microsomaux"}, {"code": "B349", "name": "Anticorps anti thyroidiens: AC anti Thyroglobulines"}, {"code": "B525", "name": "Identification des anticorps antigliadine (IgG et IgA), Antitransglutaminase tissulaire (tTg) par technique luminex ou équivalent"}, {"code": "B526", "name": "Identification des anticorps anti Phospholipides: Cardiol<PERSON>ine, B2 GP1, <PERSON><PERSON><PERSON>bine, Phosphatidylsérine par technique Luminex ou équivalent"}, {"code": "B527", "name": "Identification des anticorps Anti-nucléaires (ANA): SSA, SSB, Sm, RNP, Scl-70, Jo-1, dsD<PERSON>, Cent-B, Histone par technique Luminex ou équivalent"}, {"code": "B528", "name": "Identification des anticorps anti Thyroglobuline et thyropéroxydase par technique luminex"}, {"code": "B529", "name": "Anticorps anti-neutrophiles cytoplasmiques (ANCA): Myelopéroxydase, Proteinase 3, GBM par technique Luminex ou équivalent"}, {"code": "B530", "name": "Anticorps anti-DNA natif - T.I<PERSON>marquage"}, {"code": "B531", "name": "Anticorps anti SSA - Si Prescription isolée"}, {"code": "B532", "name": "Anticorps anti SSB - Si Prescription isolée"}, {"code": "B533", "name": "Anticorps anti Sm - Si Prescription isolée"}, {"code": "B534", "name": "Anticorps anti Scl 70 - Si Prescription isolée"}, {"code": "B535", "name": "Anticorps anti RNP - Si Prescription isolée"}, {"code": "B536", "name": "Anticorps anti Jo1 - Si Prescription isolée"}, {"code": "B537", "name": "Anticorps anti muscle lisse - Si Prescription isolée"}, {"code": "B538", "name": "Anticorps anti-cardiolipine IgG - Si Prescription isolée"}, {"code": "B539", "name": "Anticorps anti-cardiolipine IgM - Si Prescription isolée"}, {"code": "B540", "name": "Anticorps anti-Bêta 2 glycoprotéine 1 - B2GP-IgG"}, {"code": "B541", "name": "Anticorps anti-Bêta 2 glycoprotéine 1 - B2GP-IgM"}, {"code": "B542", "name": "Anticorps anti R.endoplasmique LKM"}, {"code": "B543", "name": "Anticorps anti endomysium IgA - Si Prescription isolée"}, {"code": "B544", "name": "Anticorps anti endomysium IgG - Si Prescription isolée"}, {"code": "B545", "name": "Anticorps anti GAD - Si Prescription isolée"}, {"code": "B546", "name": "Anticorps anti gliadine IgG - Si Prescription isolée"}, {"code": "B547", "name": "Anticorps anti gliadine IgM - Si Prescription isolée"}, {"code": "B548", "name": "Anticorps anti gliadine IgA - Si Prescription isolée"}, {"code": "B549", "name": "Anticorps anti transglutaminase tissulaire - IgA - Si Prescription isolée"}, {"code": "B550", "name": "Anticorps anti transglutaminase tissulaire - IgG - Si Prescription isolée"}, {"code": "B551", "name": "Anticorps anti phospholipides - Si Prescription isolée"}, {"code": "B552", "name": "Anticorps - Saccharomyces cerevisiae (ASCA) - EIA"}, {"code": "B553", "name": "Anticorps anti-peptides cycliques citrullinés"}, {"code": "B554", "name": "Anticorps anti ilots de Langerhans"}, {"code": "B555", "name": "Anticorps anti recepteur de la TSH (TRAK)"}, {"code": "B556", "name": "CTX - Telopeptide C"}]}, {"name": "Allergie", "analyses": [{"code": "B354", "name": "IgE totales"}, {"code": "B355", "name": "IgE spécifiques ou Rast: Allergène unique"}, {"code": "B356", "name": "IgE spécifiques ou Rast: A partir du 3ème allergène (chacun)"}, {"code": "B357", "name": "Test multi allergènes"}, {"code": "B557", "name": "Test polyallergènes: pneumallergènes"}, {"code": "B558", "name": "Test polyallergènes: trophallergènes"}, {"code": "B559", "name": "Test polyallergènes: mixtes"}]}, {"name": "Autres", "analyses": [{"code": "B350", "name": "Complément total CH50"}, {"code": "B351", "name": "Dosage C3"}, {"code": "B352", "name": "Dosage C4"}, {"code": "B353", "name": "Dosage C1 Inhibiteur estérase"}, {"code": "B358", "name": "Immunofixation des Protides (ou Immunoéléctrophorèse)"}, {"code": "B560", "name": "IGF-1 (Somatomedine)"}, {"code": "B561", "name": "IGF-2"}, {"code": "B562", "name": "Protéine BENCE-JONES"}]}]}, {"name": "Marqueurs Tumoraux", "analyses": [{"code": "B359", "name": "<PERSON><PERSON><PERSON> Tu<PERSON>: antigène carcino embryonnaire: ACE"}, {"code": "B360", "name": "Marqueur Tumoral: Alpha foeto protéine: AFP"}, {"code": "B361", "name": "<PERSON><PERSON><PERSON>: Antigène prostatique spécifique: PSA"}, {"code": "B362", "name": "Marqueur Tumoral: Ca 125"}, {"code": "B363", "name": "Marqueur Tu<PERSON>al: Ca 15-3"}, {"code": "B364", "name": "Marqueur Tu<PERSON>al: Ca 19-9"}, {"code": "B365", "name": "Marqueur Tumoral: Ca 50"}, {"code": "B366", "name": "Marqueur Tumoral: Ca 724"}, {"code": "B367", "name": "Marqueur Tumoral: NSE"}]}, {"name": "Divers", "analyses": [{"code": "B368", "name": "Test au latex"}, {"code": "B369", "name": "Réaction de Waaler Rose"}, {"code": "B370", "name": "C R P (Protéine C réactive)"}, {"code": "B371", "name": "Haptoglobine"}, {"code": "B372", "name": "Orosomucoides"}, {"code": "B373", "name": "Transferrine"}, {"code": "B374", "name": "Alpha 1 Antitrypsine"}, {"code": "B375", "name": "IgG totales"}, {"code": "B376", "name": "IgA totales"}, {"code": "B377", "name": "IgM totales"}, {"code": "B378", "name": "Crèatorrhée"}, {"code": "B379", "name": "Spermogramme"}, {"code": "B380", "name": "Spermocytogramme"}, {"code": "B381", "name": "Stéatorrhée"}, {"code": "B382", "name": "Test de HUHNER"}]}]}, {"name": "Prescriptions pouvant faire objet de transmission à l'étranger", "types": [{"name": "Examens Spécifiques", "analyses": [{"code": "B383", "name": "17 Hydroxyprogestérone"}, {"code": "B384", "name": "25 Hydroxy cholecalciférol"}, {"code": "B385", "name": "ACTH"}, {"code": "B386", "name": "ADH"}, {"code": "B387", "name": "ADN Viral HBV"}, {"code": "B388", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": "B389", "name": "Aluminium"}, {"code": "B390", "name": "Androstenédiol"}, {"code": "B391", "name": "Anticorps Anti cannaux biliaires"}, {"code": "B392", "name": "Anticorps Anti Cartilage"}, {"code": "B393", "name": "Anticorps Anti cellules nerveuses"}, {"code": "B394", "name": "Anticorps Anti E C T"}, {"code": "B395", "name": "Anticorps Anti Facteur intrinsèque"}, {"code": "B396", "name": "Anticorps Anti organes(autres)"}, {"code": "B397", "name": "Anticorps Anti Plaquettes fixées"}, {"code": "B398", "name": "Anticorps Anti T3"}, {"code": "B399", "name": "Anticorps Anti T4"}, {"code": "B400", "name": "ARN Viral HCV"}, {"code": "B401", "name": "Ca 50"}, {"code": "B402", "name": "Calcitonine"}, {"code": "B403", "name": "Carnitine libre"}, {"code": "B404", "name": "Charge Virale H IV"}, {"code": "B405", "name": "Citrate dans le sperme"}, {"code": "B406", "name": "Complément C2"}, {"code": "B407", "name": "Complément C5"}, {"code": "B408", "name": "Delta 4 Androsténe diane"}, {"code": "B409", "name": "DHA"}, {"code": "B410", "name": "DHA Sulfate"}, {"code": "B411", "name": "DHT"}, {"code": "B412", "name": "EBV: EARL y OU VCA(lgG+lgM) ou EBNA chacun"}, {"code": "B413", "name": "ECA"}, {"code": "B414", "name": "Elastase"}, {"code": "B415", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"code": "B416", "name": "Fibronectine"}, {"code": "B417", "name": "Gastrine"}, {"code": "B418", "name": "G H ou STH"}, {"code": "B419", "name": "Glucagon"}, {"code": "B420", "name": "Hépatite E"}, {"code": "B421", "name": "Hépatite Delta"}, {"code": "B422", "name": "Hydroxyproline"}, {"code": "B423", "name": "IgG 4(sous classe)"}, {"code": "B424", "name": "Il Deoxycortisol"}, {"code": "B425", "name": "Médicaments(digoxine théophylline) chacun"}, {"code": "B426", "name": "<PERSON>"}, {"code": "B427", "name": "Ostéocalcine"}, {"code": "B428", "name": "Parathormone(PTH)"}, {"code": "B429", "name": "Peptide C"}, {"code": "B430", "name": "<PERSON><PERSON><PERSON>"}, {"code": "B431", "name": "Sérotonine"}, {"code": "B432", "name": "Somatomédine C"}, {"code": "B433", "name": "TBG"}, {"code": "B434", "name": "Te BG"}, {"code": "B435", "name": "Test à la STH"}, {"code": "B436", "name": "Thyrocalcitonine"}, {"code": "B437", "name": "Virus(culture)"}, {"code": "B438", "name": "Vitamine B 12"}, {"code": "B439", "name": "Vitamine D"}, {"code": "B440", "name": "Autres vitamines(K-A-E) chacune"}, {"code": "B441", "name": "Zinc dans le sperme"}]}]}]}