import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import { schema } from '@ioc:Adonis/Core/Validator';
import HelperController from "../helpers/HelperController";
import { ApiResponse } from 'App/Controllers/interfaces';
import User from 'App/Models/User';
import Hash from '@ioc:Adonis/Core/Hash';
import { DateTime } from 'luxon';

export default class AuthController extends HelperController {

  public async login({ auth, request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    }
    try {
      const payload = await request.validate({
        schema: schema.create({
          username: schema.string({ trim: true }),
          password: schema.string({ trim: true })
        }),
        messages: {
          'username.required': "Le nom utilisateur ou le numero de téléphone est requis",
          'password.required': "Veuillez saisir votre mot de passe correct"
        }
      });

      const user = await User.query().where('email', payload.username).orWhere('phone', payload.username).preload('role').first();

      if (!user || user == null) {
        apiResponse.message = "Identifiant ou mot de passe incorrect";
        apiResponse.except = user;
        return response.status(401).json(apiResponse);
      }
      if (user.roleId != 1 && user.roleId != 2) {
        apiResponse.message = "Vous n'êtes pas autoriser à acceder à cette ressource";
        return response.status(401).json(apiResponse);
      }
      let checkPwd = await Hash.verify(user.password, payload.password);
      if (!checkPwd) {
        apiResponse.message = "Identifiant ou mot de passe incorrect";
        apiResponse.except = checkPwd;
        return response.status(401).json(apiResponse);
      }

      user.online = 1;
      await user.save();
      const new_connection = await user.related('userHistories').create({
        userId: user.id,
        connectedAt: DateTime.now(),
        ipAddress: request.ip(),
        userAgent: request.header('user-agent') || '',
        deviceType: request.header('user-agent'),
        type: "login",
      });
      const token = await auth.use('api').generate(user, {
        expiresIn: '1day',
        name: user.username,
      });
      apiResponse.success = true;
      apiResponse.message = "Connexion reussie";
      apiResponse.result = {
        token: token,
        user: user,
        connection: new_connection
      }
      return response.status(200).json(apiResponse);
    } catch (error) {
      console.log("error", error);
      apiResponse = {
        success: false,
        message: "Une erreur s'est produite lors de la connexion",
        result: null,
        errors: error.messages,
        except: error.message
      }
      return response.status(500).json(apiResponse);
    }

  }

  public async logout({ auth, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      errors: null
    }
    try {
      const user = await auth.authenticate();
      if (user == null) {
        apiResponse.message = "Vous n'êtes pas connecté";
        return response.status(401).json(apiResponse);
      }
      const isLogout  = await auth.logout(user);
      await user.merge({
        online: 0,
      }).save();
      apiResponse.success = true;
      apiResponse.message = "Déconnexion reussie";
      apiResponse.result = isLogout;
      return response.status(200).json(apiResponse);
    } catch (error) {
      apiResponse = {
        success: false,
        message: "Une erreur s'est produite lors de la déconnexion",
        result: null,
        errors: error.messages
      }
      return response.status(500).json(apiResponse);
    }
  }
}

