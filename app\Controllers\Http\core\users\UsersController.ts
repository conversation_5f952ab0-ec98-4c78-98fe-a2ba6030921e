import { schema } from '@ioc:Adonis/Core/Validator';
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { ApiResponse, UserStatus } from 'App/Controllers/interfaces'
import Identity from 'App/Models/Identity';
import User from 'App/Models/User';
import Patient from 'App/Models/Patient';
import Soignant from 'App/Models/Soignant';
import Wallet from 'App/Models/Wallet';
import HelperController from '../../helpers/HelperController';

export default class UsersController extends HelperController {

  public async getUsers({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);

      const users = await User.query().whereNotIn('role_id', [1, 2]).preload('role').paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Success",
        result: users,
      };
    } catch (error) {
      status = 500;
      apiResponse = {
        success: false,
        message: "Internal server error",
        result: null,
        except: error.message
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async getUserIdentities({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const page = request.input('page', 1);
      const limit = request.input('limit', 10);
      const userId = request.input('user_id', null);
      const statusFilter = request.input('status', null) as 'activated' | 'archived' | 'validated' | 'rejected' | 'blocked' | 'pending' | 'kyc';

      // Create filters object only with defined and non-null values
      const filters: Record<string, any> = {};
      if (userId !== null && userId !== undefined) {
        filters.user_id = userId;
      }
      if (statusFilter !== null && statusFilter !== undefined) {
        filters.status = statusFilter;
      }

      const query = Identity.query().orderBy('created_at', 'desc').preload('user').preload('patient').preload('pro');

      // Apply filters only if they exist and have defined values
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          query.where(key, value);
        }
      });

      const jsonData = await query.paginate(page, limit);
      const data = jsonData.toJSON().data;
      const phoneNumbers = new Set();
      const identities = data.reduce((acc: any[], identity: any) => {
        // Only keep the latest KYC verification for each phone number
        if (!phoneNumbers.has(identity.phone)) {
          phoneNumbers.add(identity.phone);
          acc.push(identity);
        } else if (identity.status !== 'kyc') {
          // Keep all non-KYC status records
          acc.push(identity);
        }
        return acc;
      }, []);
      const total = identities.length;
      // Update the meta object with the total count
      jsonData.toJSON().meta.total = total;
      jsonData.toJSON().meta.perPage = limit;
      jsonData.toJSON().meta.currentPage = page;

      apiResponse = {
        success: true,
        message: "Liste des identités trouvées",
        result: {
          data: identities,
          meta: jsonData.toJSON().meta
        }
      }
    } catch (error) {
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de récupération des données",
        result: null,
        except: error.message
      }
    }
    return response.status(status).json(apiResponse);
  }

  public async validateOwnerUserIdentity({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 201;
    try {
      const payload = await request.validate({
        schema: schema.create({
          phone: schema.string(),
          status: schema.enum(['validated', 'rejected', 'blocked'])
        })
      });

      const { phone, status: status_user } = payload;
      const identity = await Identity.query().where('phone', phone).orderBy('created_at', 'desc').forUpdate().first();
      if (!identity) {
        apiResponse = {
          success: false,
          message: "Aucune demande d'identité trouvée",
          result: null
        }
        return response.status(404).json(apiResponse);
      }
      if (identity.userId === null) {
        status = 400;
        apiResponse = {
          success: false,
          message: "Données de validations incorrectes",
          result: null
        }
        return response.status(status).json(apiResponse);
      }
      const user = await User.query().where('id', identity.userId).forUpdate().first();
      if (!user) {
        apiResponse = {
          success: false,
          message: "Aucun utilisateur trouvé pour cette identité",
          result: null
        }
        return response.status(404).json(apiResponse);
      }

      await identity.merge({
        status: status_user as any
      }).save();

      apiResponse = {
        success: true,
        message: "Mise à jour du status effectué avec succès",
        result: identity
      }

      if (status_user == "validated") {

        if (user) {
          await user.merge({
            status: UserStatus.ValidatedIdentity
          }).save();
          const roleId = user.roleId;
          if (roleId === 6) { //patient
            await Patient.query()
              .where('user_id', user.id)
              .update({ status: 'activated' });
          } else if (roleId === 3) { //pro
            await Soignant.query()
              .where('user_id', user.id)
              .update({ status: 'validated' });
          }
          apiResponse = {
            success: true,
            message: "Identité du compte " + identity.lastName + identity.firstName + " a été validée avec succès",
            result: user
          }
        }
      }
      return response.status(201).json(apiResponse);
    } catch (error) {
      console.log("error", error.message);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de validation des données",
        result: null,
        except: error.message
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async validateAllUserStatus({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const newUsers = await User.query()
        .whereNotNull('phone_verified_at')
        .where('status', UserStatus.Actived)
        .where('is_old', false)
        .where('role_id', 6)
        .forUpdate();

      if (newUsers.length > 0) {
        // Get all pending patients for these users in a single query
        const userIds = newUsers.map(user => user.id);
        const pendingPatients = await Patient.query()
          .whereIn('user_id', userIds)
          .where('status', 'pending')
          .forUpdate();

        // Batch update all pending patients
        if (pendingPatients.length > 0) {
          await Patient.query()
            .whereIn('id', pendingPatients.map(patient => patient.id))
            .update({ status: 'activated' });
        }
      }
      apiResponse = {
        success: true,
        message: "Mise à jour du status effectué avec succès",
        result: newUsers.length
      }
      return response.status(201).json(apiResponse);
    } catch (error) {
      console.log("error", error);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de validation des données",
        result: null,
        except: error.message
      }
      return response.status(status).json(apiResponse);
    }
  }

  public async fixProUserWallet({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    let status = 200;
    try {
      const users = await User.query()
        .where('role_id', 3)
        .where('status', UserStatus.Actived)
        .forUpdate();

      if (users.length > 0) {
        const userIds = users.map(user => user.id);

        // Get all soignants without wallets in a single query
        const soignantsWithoutWallets = await Soignant.query()
          .whereIn('user_id', userIds)
          .whereNotExists(builder => {
            builder
              .from('wallets')
              .whereColumn('wallets.user_id', 'soignants.user_id')
              .where('wallets.owner_type', 'soignant');
          })
          .forUpdate();

        // Bulk create wallets for soignants that don't have one
        if (soignantsWithoutWallets.length > 0) {
          const walletsToCreate = await Promise.all(
            soignantsWithoutWallets.map(async soignant => ({
              userId: soignant.userId,
              ownerType: 'soignant',
              ownerId: soignant.id,
              libelle: "DO PRO WALLET",
              typeWalletId: 2,
              code: await this.generateWalletCode(),
            }))
          );
          await Wallet.createMany(walletsToCreate);
        }
        apiResponse = {
          success: true,
          message: "Mise à jour du status effectué avec succès",
          result: users.length
        }
        return response.status(201).json(apiResponse);
      } else {
        apiResponse = {
          success: true,
          message: "Pas de donnée à traiter",
          result: null
        }
        return response.status(200).json(apiResponse);
      }
    } catch (error) {
      console.log("error", error);
      status = 500;
      apiResponse = {
        success: false,
        message: "Echec de validation des données",
        result: null,
        except: error.message
      }
      return response.status(status).json(apiResponse);
    }
  }
}
