import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import Patient from './Patient'
import Soignant from './Soignant'

export default class Identity extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'last_name' })
  public lastName: string

  @column({ columnName: 'first_name' })
  public firstName: string

  @column({ columnName: 'gender' })
  public gender: 'M' | 'F' | null

  @column({ columnName: 'phone' })
  public phone: string | null

  @column({ columnName: 'email' })
  public email: string | null

  @column({ columnName: 'contact' })
  public contact: string | null

  @column({ columnName: 'address' })
  public address: string | null

  @column({ columnName: 'user_id' })
  public userId: number | null

  @column({ columnName: 'owner_type' })
  public ownerType: 'patient' | 'soignant' | 'user'

  @column({ columnName: 'owner_id' })
  public ownerId: number | null

  @column({ columnName: 'status' })
  public status: 'activated' | 'validated' | 'rejected' | 'archived' | 'blocked' | 'pending' | 'kyc'

  @column({ columnName: 'code' })
  public code: string | null

  @belongsTo(() => User,{
    foreignKey: 'userId',
    localKey: 'id'
  })
  public user: BelongsTo<typeof User>

  @belongsTo(() => Patient, {
    foreignKey: 'ownerId',
    localKey: 'id',
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => Soignant, {
    foreignKey: 'ownerId',
    localKey: 'id',
  })
  public pro: BelongsTo<typeof Soignant>
}
