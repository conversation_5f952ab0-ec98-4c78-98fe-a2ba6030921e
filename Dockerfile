ARG NODE_IMAGE=node:20-alpine
FROM $NODE_IMAGE AS base
WORKDIR /home/<USER>/app

# Mettre à jour npm et ignorer l'erreur Yarn (car déjà installé)
RUN npm install -g npm@latest || true

# Continuer avec Yarn
COPY package.json yarn.lock ./
RUN yarn install 
COPY . .

# Définir les variables d'environnement pour le projet
ENV HOST=0.0.0.0
ENV PORT=$PORT

# Étape de développement
FROM base AS dev
ENV CHOKIDAR_USEPOLLING=true
ENV NODE_ENV=development
CMD ["node", "ace", "serve", "--watch", "--node-args=--inspect=0.0.0.0"]
