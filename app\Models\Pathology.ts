import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm';
import PathologyType from './PathologyType';

export default class Pathology extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'pathology_type_id' })
  public pathologyTypeId: number | null

  @column({ columnName: 'parent_id' })
  public parentId: number | null

  @column({ columnName: 'is_leaf' })
  public isLeaf: boolean

  @column({ columnName: 'level' })
  public level: number | null

  @column()
  public name: string

  @column({ columnName: 'eng_name' })
  public engName: string | null

  @column()
  public code: string | null

  @column()
  public description: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Pathology, {
    foreignKey: 'parentId',
    localKey: 'id'
  })
  public parent: BelongsTo<typeof Pathology>

  @belongsTo(() => PathologyType, {
    foreignKey: 'pathologyTypeId',
    localKey: 'id'
  })
  public type: BelongsTo<typeof PathologyType>
}
